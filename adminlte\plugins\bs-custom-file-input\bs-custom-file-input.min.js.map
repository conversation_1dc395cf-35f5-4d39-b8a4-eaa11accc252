{"version": 3, "file": "bs-custom-file-input.min.js", "sources": ["../src/selector.js", "../src/util.js", "../src/eventHandlers.js", "../src/index.js"], "sourcesContent": ["const Selector = {\n  CUSTOMFILE: '.custom-file input[type=\"file\"]',\n  CUSTOMFILELABEL: '.custom-file-label',\n  FORM: 'form',\n  INPUT: 'input',\n}\n\nexport default Selector\n", "import Selector from './selector'\n\nconst textNodeType = 3\nconst getDefaultText = (input) => {\n  let defaultText = ''\n\n  const label = input.parentNode.querySelector(Selector.CUSTOMFILELABEL)\n\n  if (label) {\n    defaultText = label.textContent\n  }\n\n  return defaultText\n}\n\nconst findFirstChildNode = (element) => {\n  if (element.childNodes.length > 0) {\n    const childNodes = [].slice.call(element.childNodes)\n\n    for (let i = 0; i < childNodes.length; i++) {\n      const node = childNodes[i]\n      if (node.nodeType !== textNodeType) {\n        return node\n      }\n    }\n  }\n\n  return element\n}\n\nconst restoreDefaultText = (input) => {\n  const defaultText = input.bsCustomFileInput.defaultText\n  const label = input.parentNode.querySelector(Selector.CUSTOMFILELABEL)\n\n  if (label) {\n    const element = findFirstChildNode(label)\n\n    element.textContent = defaultText\n  }\n}\n\nexport {\n  getDefaultText,\n  findFirstChildNode,\n  restoreDefaultText,\n}\n", "import { findFirstChildNode, restoreDefaultText } from './util'\nimport Selector from './selector'\n\nconst fileApi = !!window.File\nconst FAKE_PATH = 'fakepath'\nconst FAKE_PATH_SEPARATOR = '\\\\'\n\nconst getSelectedFiles = (input) => {\n  if (input.hasAttribute('multiple') && fileApi) {\n    return [].slice.call(input.files)\n      .map((file) => file.name)\n      .join(', ')\n  }\n\n  if (input.value.indexOf(FAKE_PATH) !== -1) {\n    const splittedValue = input.value.split(FAKE_PATH_SEPARATOR)\n\n    return splittedValue[splittedValue.length - 1]\n  }\n\n  return input.value\n}\n\nfunction handleInputChange() {\n  const label = this.parentNode.querySelector(Selector.CUSTOMFILELABEL)\n\n  if (label) {\n    const element = findFirstChildNode(label)\n    const inputValue = getSelectedFiles(this)\n\n    if (inputValue.length) {\n      element.textContent = inputValue\n    } else {\n      restoreDefaultText(this)\n    }\n  }\n}\n\nfunction handleFormReset() {\n  const customFileList = [].slice.call(this.querySelectorAll(Selector.INPUT))\n    .filter((input) => !!input.bsCustomFileInput)\n\n  for (let i = 0, len = customFileList.length; i < len; i++) {\n    restoreDefaultText(customFileList[i])\n  }\n}\n\nexport {\n  handleInputChange,\n  handleFormReset,\n}\n", "import { getDefaultText, restoreDefaultText } from './util'\nimport { handleFormReset, handleInputChange } from './eventHandlers'\nimport Selector from './selector'\n\nconst customProperty = 'bsCustomFileInput'\nconst Event = {\n  FORMRESET   : 'reset',\n  INPUTCHANGE : 'change',\n}\n\nconst bsCustomFileInput = {\n  init(inputSelector = Selector.CUSTOMFILE, formSelector = Selector.FORM) {\n    const customFileInputList = [].slice.call(document.querySelectorAll(inputSelector))\n    const formList = [].slice.call(document.querySelectorAll(formSelector))\n\n    for (let i = 0, len = customFileInputList.length; i < len; i++) {\n      const input = customFileInputList[i]\n\n      Object.defineProperty(input, customProperty, {\n        value: {\n          defaultText: getDefaultText(input),\n        },\n        writable: true,\n      })\n\n      handleInputChange.call(input)\n      input.addEventListener(Event.INPUTCHANGE, handleInputChange)\n    }\n\n    for (let i = 0, len = formList.length; i < len; i++) {\n      formList[i].addEventListener(Event.FORMRESET, handleFormReset)\n      Object.defineProperty(formList[i], customProperty, {\n        value: true,\n        writable: true,\n      })\n    }\n  },\n\n  destroy() {\n    const formList = [].slice.call(document.querySelectorAll(Selector.FORM))\n      .filter((form) => !!form.bsCustomFileInput)\n    const customFileInputList = [].slice.call(document.querySelectorAll(Selector.INPUT))\n      .filter((input) => !!input.bsCustomFileInput)\n\n    for (let i = 0, len = customFileInputList.length; i < len; i++) {\n      const input = customFileInputList[i]\n\n      restoreDefaultText(input)\n      input[customProperty] = undefined\n\n      input.removeEventListener(Event.INPUTCHANGE, handleInputChange)\n    }\n\n    for (let i = 0, len = formList.length; i < len; i++) {\n      formList[i].removeEventListener(Event.FORMRESET, handleFormReset)\n      formList[i][customProperty] = undefined\n    }\n  },\n}\n\nexport default bsCustomFileInput\n"], "names": ["Selector", "CUSTOMFILE", "CUSTOMFILELABEL", "FORM", "INPUT", "findFirstChildNode", "element", "childNodes", "length", "slice", "call", "i", "node", "nodeType", "restoreDefaultText", "input", "defaultText", "bsCustomFileInput", "label", "parentNode", "querySelector", "textContent", "fileApi", "window", "File", "getSelectedFiles", "hasAttribute", "files", "map", "file", "name", "join", "value", "indexOf", "splittedValue", "split", "handleInputChange", "this", "inputValue", "handleFormReset", "customFileList", "querySelectorAll", "filter", "len", "customProperty", "Event", "init", "inputSelector", "formSelector", "customFileInputList", "document", "formList", "Object", "defineProperty", "writable", "addEventListener", "destroy", "form", "undefined", "removeEventListener"], "mappings": ";;;;;uMAAA,IAAMA,EAAW,CACfC,WAAY,kCACZC,gBAAiB,qBACjBC,KAAM,OACNC,MAAO,SCWHC,EAAqB,SAACC,MACM,EAA5BA,EAAQC,WAAWC,eACfD,EAAa,GAAGE,MAAMC,KAAKJ,EAAQC,YAEhCI,EAAI,EAAGA,EAAIJ,EAAWC,OAAQG,IAAK,KACpCC,EAAOL,EAAWI,MAlBT,IAmBXC,EAAKC,gBACAD,SAKNN,GAGHQ,EAAqB,SAACC,OACpBC,EAAcD,EAAME,kBAAkBD,YACtCE,EAAQH,EAAMI,WAAWC,cAAcpB,EAASE,iBAElDgB,IACcb,EAAmBa,GAE3BG,YAAcL,IClCpBM,IAAYC,OAAOC,KAInBC,EAAmB,SAACV,MACpBA,EAAMW,aAAa,aAAeJ,QAC7B,GAAGb,MAAMC,KAAKK,EAAMY,OACxBC,IAAI,SAACC,UAASA,EAAKC,OACnBC,KAAK,UAG8B,IAApChB,EAAMiB,MAAMC,QAVA,mBAgBTlB,EAAMiB,UALLE,EAAgBnB,EAAMiB,MAAMG,MAVV,aAYjBD,EAAcA,EAAc1B,OAAS,IAMhD,SAAS4B,QACDlB,EAAQmB,KAAKlB,WAAWC,cAAcpB,EAASE,oBAEjDgB,EAAO,KACHZ,EAAUD,EAAmBa,GAC7BoB,EAAab,EAAiBY,MAEhCC,EAAW9B,OACbF,EAAQe,YAAciB,EAEtBxB,EAAmBuB,OAKzB,SAASE,YACDC,EAAiB,GAAG/B,MAAMC,KAAK2B,KAAKI,iBAAiBzC,EAASI,QACjEsC,OAAO,SAAC3B,WAAYA,EAAME,oBAEpBN,EAAI,EAAGgC,EAAMH,EAAehC,OAAQG,EAAIgC,EAAKhC,IACpDG,EAAmB0B,EAAe7B,ICvCtC,IAAMiC,EAAiB,oBACjBC,EACU,QADVA,EAEU,eAGU,CACxBC,cAAKC,EAAqCC,YAArCD,IAAAA,EAAgB/C,EAASC,qBAAY+C,IAAAA,EAAehD,EAASG,cFP9Da,EAEEE,EEME+B,EAAsB,GAAGxC,MAAMC,KAAKwC,SAAST,iBAAiBM,IAC9DI,EAAW,GAAG1C,MAAMC,KAAKwC,SAAST,iBAAiBO,IAEhDrC,EAAI,EAAGgC,EAAMM,EAAoBzC,OAAQG,EAAIgC,EAAKhC,IAAK,KACxDI,EAAQkC,EAAoBtC,GAElCyC,OAAOC,eAAetC,EAAO6B,EAAgB,CAC3CZ,MAAO,CACLhB,aFhBJA,OAAAA,EAAAA,EAAc,IAEZE,EEc8BH,EFdhBI,WAAWC,cAAcpB,EAASE,oBAGpDc,EAAcE,EAAMG,aAGfL,IEUDsC,UAAU,IAGZlB,EAAkB1B,KAAKK,GACvBA,EAAMwC,iBAAiBV,EAAmBT,OAGvC,IAAIzB,EAAI,EAAGgC,EAAMQ,EAAS3C,OAAQG,EAAIgC,EAAKhC,IAC9CwC,EAASxC,GAAG4C,iBAAiBV,EAAiBN,GAC9Ca,OAAOC,eAAeF,EAASxC,GAAIiC,EAAgB,CACjDZ,OAAO,EACPsB,UAAU,KAKhBE,2BACQL,EAAW,GAAG1C,MAAMC,KAAKwC,SAAST,iBAAiBzC,EAASG,OAC/DuC,OAAO,SAACe,WAAWA,EAAKxC,oBACrBgC,EAAsB,GAAGxC,MAAMC,KAAKwC,SAAST,iBAAiBzC,EAASI,QAC1EsC,OAAO,SAAC3B,WAAYA,EAAME,oBAEpBN,EAAI,EAAGgC,EAAMM,EAAoBzC,OAAQG,EAAIgC,EAAKhC,IAAK,KACxDI,EAAQkC,EAAoBtC,GAElCG,EAAmBC,GACnBA,EAAM6B,QAAkBc,EAExB3C,EAAM4C,oBAAoBd,EAAmBT,OAG1C,IAAIzB,EAAI,EAAGgC,EAAMQ,EAAS3C,OAAQG,EAAIgC,EAAKhC,IAC9CwC,EAASxC,GAAGgD,oBAAoBd,EAAiBN,GACjDY,EAASxC,GAAGiC,QAAkBc"}