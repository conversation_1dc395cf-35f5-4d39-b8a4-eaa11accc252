{"version": 3, "sources": ["..\\..\\..\\build\\scss\\parts\\adminlte.plugins.scss", "..\\..\\..\\build\\scss\\mixins\\_animations.scss", "..\\..\\..\\build\\scss\\plugins\\_fullcalendar.scss", "dist\\css\\alt\\adminlte.plugins.css", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_breakpoints.scss", "..\\..\\..\\build\\scss\\mixins\\_miscellaneous.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_box-shadow.scss", "..\\..\\..\\build\\scss\\plugins\\_select2.scss", "..\\..\\..\\build\\scss\\plugins\\_mixins.scss", "..\\..\\..\\build\\scss\\mixins\\_dark-mode.scss", "..\\..\\..\\build\\scss\\plugins\\_bootstrap-slider.scss", "..\\..\\..\\build\\scss\\plugins\\_icheck-bootstrap.scss", "..\\..\\..\\build\\scss\\plugins\\_mapael.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_reset-text.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_border-radius.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\vendor\\_rfs.scss", "..\\..\\..\\build\\scss\\plugins\\_jqvmap.scss", "..\\..\\..\\build\\scss\\plugins\\_sweetalert2.scss", "..\\..\\..\\build\\scss\\plugins\\_toastr.scss", "..\\..\\..\\build\\scss\\plugins\\_pace.scss", "..\\..\\..\\build\\scss\\plugins\\_bootstrap-switch.scss", "..\\..\\..\\build\\scss\\plugins\\_daterangepicker.scss", "..\\..\\..\\build\\scss\\plugins\\_miscellaneous.scss"], "names": [], "mappings": "AAAA;;;;;;ACKA,2BACE,GACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBACA,2BAAA,QACA,QAAA,EAGF,IACE,kBAAA,mBAAA,uBAAA,UAAA,mBAAA,uBACA,2BAAA,QAGF,IACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBACA,QAAA,EAGF,IACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBAGF,KACE,kBAAA,mBAAA,UAAA,oBAtBJ,mBACE,GACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBACA,2BAAA,QACA,QAAA,EAGF,IACE,kBAAA,mBAAA,uBAAA,UAAA,mBAAA,uBACA,2BAAA,QAGF,IACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBACA,QAAA,EAGF,IACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBAGF,KACE,kBAAA,mBAAA,UAAA,oBAKJ,0BACE,KACE,QAAA,EAGF,GACE,QAAA,GANJ,kBACE,KACE,QAAA,EAGF,GACE,QAAA,GAIJ,2BACE,KACE,QAAA,EAGF,GACE,QAAA,GANJ,mBACE,KACE,QAAA,EAGF,GACE,QAAA,GAIJ,yBACE,GACE,kBAAA,mBAAA,UAAA,UAAA,mBAAA,UAEF,IACE,kBAAA,qBAAA,cAAA,UAAA,qBAAA,cAEF,IACE,kBAAA,kBAAA,aAAA,UAAA,kBAAA,aAEF,IACE,kBAAA,iBAAA,UAAA,UAAA,iBAAA,UAEF,IACE,kBAAA,oBAAA,aAAA,UAAA,oBAAA,aAEF,IACE,kBAAA,oBAAA,cAAA,UAAA,oBAAA,cAEF,IACE,kBAAA,oBAAA,UAAA,UAAA,oBAAA,UAEF,IACE,kBAAA,mBAAA,cAAA,UAAA,mBAAA,cAEF,IACE,kBAAA,qBAAA,aAAA,UAAA,qBAAA,aAEF,IACE,kBAAA,mBAAA,UAAA,UAAA,mBAAA,UAEF,KACE,kBAAA,oBAAA,cAAA,UAAA,oBAAA,eAhCJ,iBACE,GACE,kBAAA,mBAAA,UAAA,UAAA,mBAAA,UAEF,IACE,kBAAA,qBAAA,cAAA,UAAA,qBAAA,cAEF,IACE,kBAAA,kBAAA,aAAA,UAAA,kBAAA,aAEF,IACE,kBAAA,iBAAA,UAAA,UAAA,iBAAA,UAEF,IACE,kBAAA,oBAAA,aAAA,UAAA,oBAAA,aAEF,IACE,kBAAA,oBAAA,cAAA,UAAA,oBAAA,cAEF,IACE,kBAAA,oBAAA,UAAA,UAAA,oBAAA,UAEF,IACE,kBAAA,mBAAA,cAAA,UAAA,mBAAA,cAEF,IACE,kBAAA,qBAAA,aAAA,UAAA,qBAAA,aAEF,IACE,kBAAA,mBAAA,UAAA,UAAA,mBAAA,UAEF,KACE,kBAAA,oBAAA,cAAA,UAAA,oBAAA,eAIJ,0BACE,GACE,kBAAA,KAAA,UAAA,KAGF,IACE,kBAAA,sBAAA,sBAAA,UAAA,sBAAA,sBAGF,IACE,kBAAA,qBAAA,qBAAA,UAAA,qBAAA,qBAGF,IACE,kBAAA,sBAAA,sBAAA,UAAA,sBAAA,sBAGF,IACE,kBAAA,qBAAA,qBAAA,UAAA,qBAAA,qBAGF,IACE,kBAAA,qBAAA,sBAAA,UAAA,qBAAA,sBAGF,KACE,kBAAA,KAAA,UAAA,MA1BJ,kBACE,GACE,kBAAA,KAAA,UAAA,KAGF,IACE,kBAAA,sBAAA,sBAAA,UAAA,sBAAA,sBAGF,IACE,kBAAA,qBAAA,qBAAA,UAAA,qBAAA,qBAGF,IACE,kBAAA,sBAAA,sBAAA,UAAA,sBAAA,sBAGF,IACE,kBAAA,qBAAA,qBAAA,UAAA,qBAAA,qBAGF,IACE,kBAAA,qBAAA,sBAAA,UAAA,qBAAA,sBAGF,KACE,kBAAA,KAAA,UAAA,MC7GJ,WACE,WAAA,QACA,iBAAA,KACA,oBAAA,KACA,aAAA,KACA,MAAA,QALF,iBAAA,kBAAA,iBAUI,iBAAA,QAKJ,oBACE,MAAA,KACA,UAAA,KACA,YAAA,MACA,YAAA,KAGF,iBACE,cAAA,KAGF,gBACE,aAAA,KAIF,kBACE,WAAA,QAGF,SACE,OAAA,EACA,MAAA,KCuPF,iCDpPA,gCAEE,YAAA,EACA,aAAA,ECuPF,gCDpPA,+BAEE,aAAA,EAGF,YCoPA,8BDlPE,OAAA,EACA,QAAA,KEcE,4BFVF,YACE,mBAAA,OAAA,eAAA,OADF,qBAII,eAAA,EAAA,MAAA,EACA,cAAA,MALJ,uBASI,eAAA,EAAA,MAAA,EACA,cAAA,QAVJ,sBAcI,eAAA,EAAA,MAAA,GAKN,eACE,UAAA,KACA,YAAA,IACA,cAAA,KAGF,iBACE,WAAA,KACA,OAAA,EACA,QAAA,EAHF,oBAMI,MAAA,KACA,UAAA,KACA,YAAA,KACA,aAAA,IATJ,wBCoQA,yBAEA,yBADA,yBAFA,yBADA,yBAMA,yBADA,oCDpPM,WAAA,kBAAA,OAAA,IAAA,WAAA,UAAA,OAAA,IAAA,WAAA,UAAA,OAAA,GAAA,CAAA,kBAAA,OAAA,IAnBN,8BCiRA,+BAEA,+BADA,+BAFA,+BADA,+BAMA,+BADA,0CEzVE,kBAAA,cAAA,UAAA,cHiGF,eACE,WAAA,IAAA,OAAA,IAGF,gBIxGM,WAAA,EAAA,EAAA,IAAA,gBAAA,CAAA,EAAA,IAAA,IAAA,eJ2GJ,cAAA,OACA,OAAA,KACA,YAAA,IACA,cAAA,IACA,QAAA,IAAA,KAPF,sBIxGM,WAAA,MAAA,EAAA,EAAA,KAAA,eCFN,uDAEI,OAAA,IAAA,MAAA,QAEA,QAAA,UAAA,OACA,OAAA,oBALJ,+EAUM,aAAA,QAVN,8CAeI,OAAA,IAAA,MAAA,QAfJ,qDAoBI,QAAA,IAAA,KACA,oBAAA,KAAA,iBAAA,KAAA,gBAAA,KAAA,YAAA,KArBJ,oFAyBI,aAAA,EAEA,OAAA,KACA,WAAA,KA5BJ,6FAgCI,cAAA,IACA,aAAA,KAjCJ,iFAqCI,OAAA,KACA,MAAA,IAtCJ,mFA0CI,WAAA,EA1CJ,qEJgaA,2EIhXM,OAAA,IAAA,MAAA,QAhDN,2EJqaA,iFIlXQ,QAAA,EACA,OAAA,IAAA,MAAA,QApDR,sEA2DM,WAAA,EA3DN,sEA+DM,cAAA,EA/DN,yEAqEM,MAAA,QArEN,yEA2EM,iBAAA,QA3EN,yEAAA,+EA+EQ,MAAA,QA/ER,kEAsFI,iBAAA,QACA,MAAA,KAvFJ,iFAAA,uFA8FQ,iBAAA,QACA,MAAA,KA/FR,yDAuGM,OAAA,IAAA,MAAA,QACA,WAAA,oBAxGN,+DA2GQ,aAAA,QA3GR,sFA+GQ,QAAA,EAAA,QAAA,QACA,cAAA,SAhHR,2IAmHU,MAAA,KACA,YAAA,QApHV,kKAuHY,MAAA,eAvHZ,oJA8HY,OAAA,EACA,WAAA,IA/HZ,oFAqIQ,iBAAA,QACA,aAAA,QACA,MAAA,KACA,QAAA,EAAA,KACA,WAAA,OAzIR,4FA6IQ,MAAA,qBACA,MAAA,MACA,YAAA,IACA,aAAA,KAhJR,kGAmJU,MAAA,KAIJ,+HAAA,gIAIM,WAAA,IAJN,4FAAA,6FASI,WAAA,MJgWV,kFIhgBA,gFAwKQ,aAAA,QAxKR,4EA4KQ,OAAA,EA5KR,uFAkLI,cAAA,KAGF,oEAEI,0BAAA,EACA,uBAAA,EAIJ,6EAEI,2BAAA,EACA,wBAAA,EAMN,2EAEI,WAAA,KAKJ,mDAEI,UAAA,IAIJ,gEJ6UA,8EIzUM,OAAA,sBAJN,6FJkVA,2GI3UQ,WAAA,OAPR,0FJuVA,wGI5UQ,IAAA,QAXR,kEJ4VA,gFI5UM,WAAA,sBAhBN,+FJiWA,6GI9UQ,QAAA,EAAA,OAAA,OACA,WAAA,OApBR,oJJuWA,kKIhVU,YAAA,OAvBV,6JJ4WA,2KIhVY,WAAA,IASZ,kCACE,QAAA,KChQA,gGAKQ,aAAA,QALR,iGAUM,aAAA,QL6kBR,4FACA,kGK1kBI,2FL4kBJ,4FACA,kGAFA,2FKpkBY,OAAA,IAAA,MAAA,QAPR,mFLklBJ,mFKrkBQ,iBAAA,QACA,MAAA,KAdJ,kGAAA,wGLwlBJ,kGACA,wGKtkBY,iBAAA,QACA,MAAA,KApBR,gFL+lBJ,gFKlkBY,aAAA,QA7BR,qGLomBJ,qGKnkBY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,6GL2mBJ,6GKpkBY,MAAA,qBAvCR,mHLgnBJ,mHKtkBc,MAAA,KA1CV,mGLqnBJ,mGKrkBU,aAAA,QA9DR,kGAKQ,aAAA,QALR,mGAUM,aAAA,QLsoBR,8FACA,oGKnoBI,6FLqoBJ,8FACA,oGAFA,6FK7nBY,OAAA,IAAA,MAAA,QAPR,qFL2oBJ,qFK9nBQ,iBAAA,QACA,MAAA,KAdJ,oGAAA,0GLipBJ,oGACA,0GK/nBY,iBAAA,QACA,MAAA,KApBR,kFLwpBJ,kFK3nBY,aAAA,QA7BR,uGL6pBJ,uGK5nBY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,+GLoqBJ,+GK7nBY,MAAA,qBAvCR,qHLyqBJ,qHK/nBc,MAAA,KA1CV,qGL8qBJ,qGK9nBU,aAAA,QA9DR,gGAKQ,aAAA,QALR,iGAUM,aAAA,QL+rBR,4FACA,kGK5rBI,2FL8rBJ,4FACA,kGAFA,2FKtrBY,OAAA,IAAA,MAAA,QAPR,mFLosBJ,mFKvrBQ,iBAAA,QACA,MAAA,KAdJ,kGAAA,wGL0sBJ,kGACA,wGKxrBY,iBAAA,QACA,MAAA,KApBR,gFLitBJ,gFKprBY,aAAA,QA7BR,qGLstBJ,qGKrrBY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,6GL6tBJ,6GKtrBY,MAAA,qBAvCR,mHLkuBJ,mHKxrBc,MAAA,KA1CV,mGLuuBJ,mGKvrBU,aAAA,QA9DR,6FAKQ,aAAA,QALR,8FAUM,aAAA,QLwvBR,yFACA,+FKrvBI,wFLuvBJ,yFACA,+FAFA,wFK/uBY,OAAA,IAAA,MAAA,QAPR,gFL6vBJ,gFKhvBQ,iBAAA,QACA,MAAA,KAdJ,+FAAA,qGLmwBJ,+FACA,qGKjvBY,iBAAA,QACA,MAAA,KApBR,6EL0wBJ,6EK7uBY,aAAA,QA7BR,kGL+wBJ,kGK9uBY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,0GLsxBJ,0GK/uBY,MAAA,qBAvCR,gHL2xBJ,gHKjvBc,MAAA,KA1CV,gGLgyBJ,gGKhvBU,aAAA,QA9DR,gGAKQ,aAAA,QALR,iGAUM,aAAA,QLizBR,4FACA,kGK9yBI,2FLgzBJ,4FACA,kGAFA,2FKxyBY,OAAA,IAAA,MAAA,QAPR,mFLszBJ,mFKzyBQ,iBAAA,QACA,MAAA,QAdJ,kGAAA,wGL4zBJ,kGACA,wGK1yBY,iBAAA,QACA,MAAA,QApBR,gFLm0BJ,gFKtyBY,aAAA,QA7BR,qGLw0BJ,qGKvyBY,iBAAA,QACA,aAAA,QACA,MAAA,QAnCR,6GL+0BJ,6GKxyBY,MAAA,kBAvCR,mHLo1BJ,mHK1yBc,MAAA,QA1CV,mGLy1BJ,mGKzyBU,aAAA,QA9DR,+FAKQ,aAAA,QALR,gGAUM,aAAA,QL02BR,2FACA,iGKv2BI,0FLy2BJ,2FACA,iGAFA,0FKj2BY,OAAA,IAAA,MAAA,QAPR,kFL+2BJ,kFKl2BQ,iBAAA,QACA,MAAA,KAdJ,iGAAA,uGLq3BJ,iGACA,uGKn2BY,iBAAA,QACA,MAAA,KApBR,+EL43BJ,+EK/1BY,aAAA,QA7BR,oGLi4BJ,oGKh2BY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,4GLw4BJ,4GKj2BY,MAAA,qBAvCR,kHL64BJ,kHKn2Bc,MAAA,KA1CV,kGLk5BJ,kGKl2BU,aAAA,QA9DR,8FAKQ,aAAA,KALR,+FAUM,aAAA,KLm6BR,0FACA,gGKh6BI,yFLk6BJ,0FACA,gGAFA,yFK15BY,OAAA,IAAA,MAAA,KAPR,iFLw6BJ,iFK35BQ,iBAAA,QACA,MAAA,QAdJ,gGAAA,sGL86BJ,gGACA,sGK55BY,iBAAA,QACA,MAAA,QApBR,8ELq7BJ,8EKx5BY,aAAA,KA7BR,mGL07BJ,mGKz5BY,iBAAA,QACA,aAAA,QACA,MAAA,QAnCR,2GLi8BJ,2GK15BY,MAAA,kBAvCR,iHLs8BJ,iHK55Bc,MAAA,QA1CV,iGL28BJ,iGK35BU,aAAA,KA9DR,6FAKQ,aAAA,QALR,8FAUM,aAAA,QL49BR,yFACA,+FKz9BI,wFL29BJ,yFACA,+FAFA,wFKn9BY,OAAA,IAAA,MAAA,QAPR,gFLi+BJ,gFKp9BQ,iBAAA,QACA,MAAA,KAdJ,+FAAA,qGLu+BJ,+FACA,qGKr9BY,iBAAA,QACA,MAAA,KApBR,6EL8+BJ,6EKj9BY,aAAA,QA7BR,kGLm/BJ,kGKl9BY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,0GL0/BJ,0GKn9BY,MAAA,qBAvCR,gHL+/BJ,gHKr9Bc,MAAA,KA1CV,gGLogCJ,gGKp9BU,aAAA,QA9DR,kGAKQ,aAAA,QALR,mGAUM,aAAA,QLqhCR,8FACA,oGKlhCI,6FLohCJ,8FACA,oGAFA,6FK5gCY,OAAA,IAAA,MAAA,QAPR,qFL0hCJ,qFK7gCQ,iBAAA,QACA,MAAA,KAdJ,oGAAA,0GLgiCJ,oGACA,0GK9gCY,iBAAA,QACA,MAAA,KApBR,kFLuiCJ,kFK1gCY,aAAA,QA7BR,uGL4iCJ,uGK3gCY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,+GLmjCJ,+GK5gCY,MAAA,qBAvCR,qHLwjCJ,qHK9gCc,MAAA,KA1CV,qGL6jCJ,qGK7gCU,aAAA,QA9DR,6FAKQ,aAAA,QALR,8FAUM,aAAA,QL8kCR,yFACA,+FK3kCI,wFL6kCJ,yFACA,+FAFA,wFKrkCY,OAAA,IAAA,MAAA,QAPR,gFLmlCJ,gFKtkCQ,iBAAA,QACA,MAAA,KAdJ,+FAAA,qGLylCJ,+FACA,qGKvkCY,iBAAA,QACA,MAAA,KApBR,6ELgmCJ,6EKnkCY,aAAA,QA7BR,kGLqmCJ,kGKpkCY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,0GL4mCJ,0GKrkCY,MAAA,qBAvCR,gHLinCJ,gHKvkCc,MAAA,KA1CV,gGLsnCJ,gGKtkCU,aAAA,QA9DR,8FAKQ,aAAA,QALR,+FAUM,aAAA,QLuoCR,0FACA,gGKpoCI,yFLsoCJ,0FACA,gGAFA,yFK9nCY,OAAA,IAAA,MAAA,QAPR,iFL4oCJ,iFK/nCQ,iBAAA,QACA,MAAA,KAdJ,gGAAA,sGLkpCJ,gGACA,sGKhoCY,iBAAA,QACA,MAAA,KApBR,8ELypCJ,8EK5nCY,aAAA,QA7BR,mGL8pCJ,mGK7nCY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,2GLqqCJ,2GK9nCY,MAAA,qBAvCR,iHL0qCJ,iHKhoCc,MAAA,KA1CV,iGL+qCJ,iGK/nCU,aAAA,QA9DR,6FAKQ,aAAA,QALR,8FAUM,aAAA,QLgsCR,yFACA,+FK7rCI,wFL+rCJ,yFACA,+FAFA,wFKvrCY,OAAA,IAAA,MAAA,QAPR,gFLqsCJ,gFKxrCQ,iBAAA,QACA,MAAA,QAdJ,+FAAA,qGL2sCJ,+FACA,qGKzrCY,iBAAA,QACA,MAAA,QApBR,6ELktCJ,6EKrrCY,aAAA,QA7BR,kGLutCJ,kGKtrCY,iBAAA,QACA,aAAA,QACA,MAAA,QAnCR,0GL8tCJ,0GKvrCY,MAAA,kBAvCR,gHLmuCJ,gHKzrCc,MAAA,QA1CV,gGLwuCJ,gGKxrCU,aAAA,QA9DR,gGAKQ,aAAA,QALR,iGAUM,aAAA,QLyvCR,4FACA,kGKtvCI,2FLwvCJ,4FACA,kGAFA,2FKhvCY,OAAA,IAAA,MAAA,QAPR,mFL8vCJ,mFKjvCQ,iBAAA,QACA,MAAA,KAdJ,kGAAA,wGLowCJ,kGACA,wGKlvCY,iBAAA,QACA,MAAA,KApBR,gFL2wCJ,gFK9uCY,aAAA,QA7BR,qGLgxCJ,qGK/uCY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,6GLuxCJ,6GKhvCY,MAAA,qBAvCR,mHL4xCJ,mHKlvCc,MAAA,KA1CV,mGLiyCJ,mGKjvCU,aAAA,QA9DR,+FAKQ,aAAA,QALR,gGAUM,aAAA,QLkzCR,2FACA,iGK/yCI,0FLizCJ,2FACA,iGAFA,0FKzyCY,OAAA,IAAA,MAAA,QAPR,kFLuzCJ,kFK1yCQ,iBAAA,QACA,MAAA,KAdJ,iGAAA,uGL6zCJ,iGACA,uGK3yCY,iBAAA,QACA,MAAA,KApBR,+ELo0CJ,+EKvyCY,aAAA,QA7BR,oGLy0CJ,oGKxyCY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,4GLg1CJ,4GKzyCY,MAAA,qBAvCR,kHLq1CJ,kHK3yCc,MAAA,KA1CV,kGL01CJ,kGK1yCU,aAAA,QA9DR,6FAKQ,aAAA,QALR,8FAUM,aAAA,QL82CR,yFACA,+FAFA,wFAFA,yFACA,+FKx2CI,wFAOQ,OAAA,IAAA,MAAA,QLy2CZ,gFKh3CI,gFAaI,iBAAA,QACA,MAAA,KLw2CR,+FACA,qGKv3CI,+FAAA,qGAmBQ,iBAAA,QACA,MAAA,KLy2CZ,6EK73CI,6EA6BQ,aAAA,QLq2CZ,kGKl4CI,kGAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLs2CZ,0GKz4CI,0GAuCQ,MAAA,qBLu2CZ,gHK94CI,gHA0CU,MAAA,KLy2Cd,gGKn5CI,gGAgDM,aAAA,QA9DR,+FAKQ,aAAA,QALR,gGAUM,aAAA,QLo6CR,2FACA,iGKj6CI,0FLm6CJ,2FACA,iGAFA,0FK35CY,OAAA,IAAA,MAAA,QAPR,kFLy6CJ,kFK55CQ,iBAAA,QACA,MAAA,KAdJ,iGAAA,uGL+6CJ,iGACA,uGK75CY,iBAAA,QACA,MAAA,KApBR,+ELs7CJ,+EKz5CY,aAAA,QA7BR,oGL27CJ,oGK15CY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,4GLk8CJ,4GK35CY,MAAA,qBAvCR,kHLu8CJ,kHK75Cc,MAAA,KA1CV,kGL48CJ,kGK55CU,aAAA,QA9DR,+FAKQ,aAAA,QALR,gGAUM,aAAA,QL69CR,2FACA,iGK19CI,0FL49CJ,2FACA,iGAFA,0FKp9CY,OAAA,IAAA,MAAA,QAPR,kFLk+CJ,kFKr9CQ,iBAAA,QACA,MAAA,KAdJ,iGAAA,uGLw+CJ,iGACA,uGKt9CY,iBAAA,QACA,MAAA,KApBR,+EL++CJ,+EKl9CY,aAAA,QA7BR,oGLo/CJ,oGKn9CY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,4GL2/CJ,4GKp9CY,MAAA,qBAvCR,kHLggDJ,kHKt9Cc,MAAA,KA1CV,kGLqgDJ,kGKr9CU,aAAA,QA9DR,6FAKQ,aAAA,QALR,8FAUM,aAAA,QLshDR,yFACA,+FKnhDI,wFLqhDJ,yFACA,+FAFA,wFK7gDY,OAAA,IAAA,MAAA,QAPR,gFL2hDJ,gFK9gDQ,iBAAA,QACA,MAAA,KAdJ,+FAAA,qGLiiDJ,+FACA,qGK/gDY,iBAAA,QACA,MAAA,KApBR,6ELwiDJ,6EK3gDY,aAAA,QA7BR,kGL6iDJ,kGK5gDY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,0GLojDJ,0GK7gDY,MAAA,qBAvCR,gHLyjDJ,gHK/gDc,MAAA,KA1CV,gGL8jDJ,gGK9gDU,aAAA,QA9DR,4FAKQ,aAAA,QALR,6FAUM,aAAA,QL+kDR,wFACA,8FK5kDI,uFL8kDJ,wFACA,8FAFA,uFKtkDY,OAAA,IAAA,MAAA,QAPR,+ELolDJ,+EKvkDQ,iBAAA,QACA,MAAA,KAdJ,8FAAA,oGL0lDJ,8FACA,oGKxkDY,iBAAA,QACA,MAAA,KApBR,4ELimDJ,4EKpkDY,aAAA,QA7BR,iGLsmDJ,iGKrkDY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,yGL6mDJ,yGKtkDY,MAAA,qBAvCR,+GLknDJ,+GKxkDc,MAAA,KA1CV,+FLunDJ,+FKvkDU,aAAA,QA9DR,+FAKQ,aAAA,QALR,gGAUM,aAAA,QLwoDR,2FACA,iGKroDI,0FLuoDJ,2FACA,iGAFA,0FK/nDY,OAAA,IAAA,MAAA,QAPR,kFL6oDJ,kFKhoDQ,iBAAA,QACA,MAAA,QAdJ,iGAAA,uGLmpDJ,iGACA,uGKjoDY,iBAAA,QACA,MAAA,KApBR,+EL0pDJ,+EK7nDY,aAAA,QA7BR,oGL+pDJ,oGK9nDY,iBAAA,QACA,aAAA,QACA,MAAA,QAnCR,4GLsqDJ,4GK/nDY,MAAA,kBAvCR,kHL2qDJ,kHKjoDc,MAAA,QA1CV,kGLgrDJ,kGKhoDU,aAAA,QA9DR,+FAKQ,aAAA,QALR,gGAUM,aAAA,QLisDR,2FACA,iGK9rDI,0FLgsDJ,2FACA,iGAFA,0FKxrDY,OAAA,IAAA,MAAA,QAPR,kFLssDJ,kFKzrDQ,iBAAA,QACA,MAAA,QAdJ,iGAAA,uGL4sDJ,iGACA,uGK1rDY,iBAAA,QACA,MAAA,QApBR,+ELmtDJ,+EKtrDY,aAAA,QA7BR,oGLwtDJ,oGKvrDY,iBAAA,QACA,aAAA,QACA,MAAA,QAnCR,4GL+tDJ,4GKxrDY,MAAA,kBAvCR,kHLouDJ,kHK1rDc,MAAA,QA1CV,kGLyuDJ,kGKzrDU,aAAA,QA9DR,8FAKQ,aAAA,QALR,+FAUM,aAAA,QL0vDR,0FACA,gGKvvDI,yFLyvDJ,0FACA,gGAFA,yFKjvDY,OAAA,IAAA,MAAA,QAPR,iFL+vDJ,iFKlvDQ,iBAAA,QACA,MAAA,KAdJ,gGAAA,sGLqwDJ,gGACA,sGKnvDY,iBAAA,QACA,MAAA,KApBR,8EL4wDJ,8EK/uDY,aAAA,QA7BR,mGLixDJ,mGKhvDY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,2GLwxDJ,2GKjvDY,MAAA,qBAvCR,iHL6xDJ,iHKnvDc,MAAA,KA1CV,iGLkyDJ,iGKlvDU,aAAA,QA9DR,6FAKQ,aAAA,QALR,8FAUM,aAAA,QLmzDR,yFACA,+FKhzDI,wFLkzDJ,yFACA,+FAFA,wFK1yDY,OAAA,IAAA,MAAA,QAPR,gFLwzDJ,gFK3yDQ,iBAAA,QACA,MAAA,KAdJ,+FAAA,qGL8zDJ,+FACA,qGK5yDY,iBAAA,QACA,MAAA,KApBR,6ELq0DJ,6EKxyDY,aAAA,QA7BR,kGL00DJ,kGKzyDY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,0GLi1DJ,0GK1yDY,MAAA,qBAvCR,gHLs1DJ,gHK5yDc,MAAA,KA1CV,gGL21DJ,gGK3yDU,aAAA,QA9DR,6FAKQ,aAAA,QALR,8FAUM,aAAA,QL42DR,yFACA,+FKz2DI,wFL22DJ,yFACA,+FAFA,wFKn2DY,OAAA,IAAA,MAAA,QAPR,gFLi3DJ,gFKp2DQ,iBAAA,QACA,MAAA,KAdJ,+FAAA,qGLu3DJ,+FACA,qGKr2DY,iBAAA,QACA,MAAA,KApBR,6EL83DJ,6EKj2DY,aAAA,QA7BR,kGLm4DJ,kGKl2DY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,0GL04DJ,0GKn2DY,MAAA,qBAvCR,gHL+4DJ,gHKr2Dc,MAAA,KA1CV,gGLo5DJ,gGKp2DU,aAAA,QA9DR,8FAKQ,aAAA,KALR,+FAUM,aAAA,KLq6DR,0FACA,gGKl6DI,yFLo6DJ,0FACA,gGAFA,yFK55DY,OAAA,IAAA,MAAA,KAPR,iFL06DJ,iFK75DQ,iBAAA,KACA,MAAA,QAdJ,gGAAA,sGLg7DJ,gGACA,sGK95DY,iBAAA,QACA,MAAA,QApBR,8ELu7DJ,8EK15DY,aAAA,KA7BR,mGL47DJ,mGK35DY,iBAAA,KACA,aAAA,QACA,MAAA,QAnCR,2GLm8DJ,2GK55DY,MAAA,kBAvCR,iHLw8DJ,iHK95Dc,MAAA,QA1CV,iGL68DJ,iGK75DU,aAAA,KA9DR,6FAKQ,aAAA,QALR,8FAUM,aAAA,QL89DR,yFACA,+FK39DI,wFL69DJ,yFACA,+FAFA,wFKr9DY,OAAA,IAAA,MAAA,QAPR,gFLm+DJ,gFKt9DQ,iBAAA,QACA,MAAA,KAdJ,+FAAA,qGLy+DJ,+FACA,qGKv9DY,iBAAA,QACA,MAAA,KApBR,6ELg/DJ,6EKn9DY,aAAA,QA7BR,kGLq/DJ,kGKp9DY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,0GL4/DJ,0GKr9DY,MAAA,qBAvCR,gHLigEJ,gHKv9Dc,MAAA,KA1CV,gGLsgEJ,gGKt9DU,aAAA,QA9DR,kGAKQ,aAAA,QALR,mGAUM,aAAA,QLuhER,8FACA,oGKphEI,6FLshEJ,8FACA,oGAFA,6FK9gEY,OAAA,IAAA,MAAA,QAPR,qFL4hEJ,qFK/gEQ,iBAAA,QACA,MAAA,KAdJ,oGAAA,0GLkiEJ,oGACA,0GKhhEY,iBAAA,QACA,MAAA,KApBR,kFLyiEJ,kFK5gEY,aAAA,QA7BR,uGL8iEJ,uGK7gEY,iBAAA,QACA,aAAA,QACA,MAAA,KAnCR,+GLqjEJ,+GK9gEY,MAAA,qBAvCR,qHL0jEJ,qHKhhEc,MAAA,KA1CV,qGL+jEJ,qGK/gEU,aAAA,QC9DN,8BF+QA,iBAAA,QACA,aAAA,QEhRA,mEFoRA,iBAAA,QEpRA,sCFwRA,iBAAA,QACA,aAAA,QEzRA,mEF4RE,MAAA,KE5RF,oDNomEJ,0DIn0DI,iBAAA,QACA,aAAA,QACA,MAAA,KEnSA,6BFsSA,iBAAA,QACA,aAAA,QACA,MAAA,KExSA,wDF2SA,iBAAA,kBACA,MAAA,QE5SA,6EF+SA,iBAAA,YACA,MAAA,KEhTA,kGFoTA,MAAA,KEpTA,2GDKM,aAAA,QCLN,4GDUI,aAAA,QLgoER,uGACA,6GAFA,sGAFA,uGACA,6GK1nEI,sGAOQ,OAAA,IAAA,MAAA,QL2nEZ,8FKloEI,8FAaI,iBAAA,QACA,MAAA,KL0nER,6GACA,mHKzoEI,6GAAA,mHAmBQ,iBAAA,QACA,MAAA,KL2nEZ,2FK/oEI,2FA6BQ,aAAA,QLunEZ,gHKppEI,gHAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLwnEZ,wHK3pEI,wHAuCQ,MAAA,qBLynEZ,8HKhqEI,8HA0CU,MAAA,KL2nEd,8GKrqEI,8GAgDM,aAAA,QC9DN,6GDKM,aAAA,QCLN,8GDUI,aAAA,QLyrER,yGACA,+GAFA,wGAFA,yGACA,+GKnrEI,wGAOQ,OAAA,IAAA,MAAA,QLorEZ,gGK3rEI,gGAaI,iBAAA,QACA,MAAA,KLmrER,+GACA,qHKlsEI,+GAAA,qHAmBQ,iBAAA,QACA,MAAA,KLorEZ,6FKxsEI,6FA6BQ,aAAA,QLgrEZ,kHK7sEI,kHAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLirEZ,0HKptEI,0HAuCQ,MAAA,qBLkrEZ,gIKztEI,gIA0CU,MAAA,KLorEd,gHK9tEI,gHAgDM,aAAA,QC9DN,2GDKM,aAAA,QCLN,4GDUI,aAAA,QLkvER,uGACA,6GAFA,sGAFA,uGACA,6GK5uEI,sGAOQ,OAAA,IAAA,MAAA,QL6uEZ,8FKpvEI,8FAaI,iBAAA,QACA,MAAA,KL4uER,6GACA,mHK3vEI,6GAAA,mHAmBQ,iBAAA,QACA,MAAA,KL6uEZ,2FKjwEI,2FA6BQ,aAAA,QLyuEZ,gHKtwEI,gHAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KL0uEZ,wHK7wEI,wHAuCQ,MAAA,qBL2uEZ,8HKlxEI,8HA0CU,MAAA,KL6uEd,8GKvxEI,8GAgDM,aAAA,QC9DN,wGDKM,aAAA,QCLN,yGDUI,aAAA,QL2yER,oGACA,0GAFA,mGAFA,oGACA,0GKryEI,mGAOQ,OAAA,IAAA,MAAA,QLsyEZ,2FK7yEI,2FAaI,iBAAA,QACA,MAAA,KLqyER,0GACA,gHKpzEI,0GAAA,gHAmBQ,iBAAA,QACA,MAAA,KLsyEZ,wFK1zEI,wFA6BQ,aAAA,QLkyEZ,6GK/zEI,6GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLmyEZ,qHKt0EI,qHAuCQ,MAAA,qBLoyEZ,2HK30EI,2HA0CU,MAAA,KLsyEd,2GKh1EI,2GAgDM,aAAA,QC9DN,2GDKM,aAAA,QCLN,4GDUI,aAAA,QLo2ER,uGACA,6GAFA,sGAFA,uGACA,6GK91EI,sGAOQ,OAAA,IAAA,MAAA,QL+1EZ,8FKt2EI,8FAaI,iBAAA,QACA,MAAA,QL81ER,6GACA,mHK72EI,6GAAA,mHAmBQ,iBAAA,QACA,MAAA,QL+1EZ,2FKn3EI,2FA6BQ,aAAA,QL21EZ,gHKx3EI,gHAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,QL41EZ,wHK/3EI,wHAuCQ,MAAA,kBL61EZ,8HKp4EI,8HA0CU,MAAA,QL+1Ed,8GKz4EI,8GAgDM,aAAA,QC9DN,0GDKM,aAAA,QCLN,2GDUI,aAAA,QL65ER,sGACA,4GAFA,qGAFA,sGACA,4GKv5EI,qGAOQ,OAAA,IAAA,MAAA,QLw5EZ,6FK/5EI,6FAaI,iBAAA,QACA,MAAA,KLu5ER,4GACA,kHKt6EI,4GAAA,kHAmBQ,iBAAA,QACA,MAAA,KLw5EZ,0FK56EI,0FA6BQ,aAAA,QLo5EZ,+GKj7EI,+GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLq5EZ,uHKx7EI,uHAuCQ,MAAA,qBLs5EZ,6HK77EI,6HA0CU,MAAA,KLw5Ed,6GKl8EI,6GAgDM,aAAA,QC9DN,yGDKM,aAAA,KCLN,0GDUI,aAAA,KLs9ER,qGACA,2GAFA,oGAFA,qGACA,2GKh9EI,oGAOQ,OAAA,IAAA,MAAA,KLi9EZ,4FKx9EI,4FAaI,iBAAA,QACA,MAAA,QLg9ER,2GACA,iHK/9EI,2GAAA,iHAmBQ,iBAAA,QACA,MAAA,QLi9EZ,yFKr+EI,yFA6BQ,aAAA,KL68EZ,8GK1+EI,8GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,QL88EZ,sHKj/EI,sHAuCQ,MAAA,kBL+8EZ,4HKt/EI,4HA0CU,MAAA,QLi9Ed,4GK3/EI,4GAgDM,aAAA,KC9DN,wGDKM,aAAA,QCLN,yGDUI,aAAA,QL+gFR,oGACA,0GAFA,mGAFA,oGACA,0GKzgFI,mGAOQ,OAAA,IAAA,MAAA,QL0gFZ,2FKjhFI,2FAaI,iBAAA,QACA,MAAA,KLygFR,0GACA,gHKxhFI,0GAAA,gHAmBQ,iBAAA,QACA,MAAA,KL0gFZ,wFK9hFI,wFA6BQ,aAAA,QLsgFZ,6GKniFI,6GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLugFZ,qHK1iFI,qHAuCQ,MAAA,qBLwgFZ,2HK/iFI,2HA0CU,MAAA,KL0gFd,2GKpjFI,2GAgDM,aAAA,QC9DN,6GDKM,aAAA,QCLN,8GDUI,aAAA,QLwkFR,yGACA,+GAFA,wGAFA,yGACA,+GKlkFI,wGAOQ,OAAA,IAAA,MAAA,QLmkFZ,gGK1kFI,gGAaI,iBAAA,QACA,MAAA,QLkkFR,+GACA,qHKjlFI,+GAAA,qHAmBQ,iBAAA,QACA,MAAA,QLmkFZ,6FKvlFI,6FA6BQ,aAAA,QL+jFZ,kHK5lFI,kHAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,QLgkFZ,0HKnmFI,0HAuCQ,MAAA,kBLikFZ,gIKxmFI,gIA0CU,MAAA,QLmkFd,gHK7mFI,gHAgDM,aAAA,QC9DN,wGDKM,aAAA,QCLN,yGDUI,aAAA,QLioFR,oGACA,0GAFA,mGAFA,oGACA,0GK3nFI,mGAOQ,OAAA,IAAA,MAAA,QL4nFZ,2FKnoFI,2FAaI,iBAAA,QACA,MAAA,KL2nFR,0GACA,gHK1oFI,0GAAA,gHAmBQ,iBAAA,QACA,MAAA,KL4nFZ,wFKhpFI,wFA6BQ,aAAA,QLwnFZ,6GKrpFI,6GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLynFZ,qHK5pFI,qHAuCQ,MAAA,qBL0nFZ,2HKjqFI,2HA0CU,MAAA,KL4nFd,2GKtqFI,2GAgDM,aAAA,QC9DN,yGDKM,aAAA,QCLN,0GDUI,aAAA,QL0rFR,qGACA,2GAFA,oGAFA,qGACA,2GKprFI,oGAOQ,OAAA,IAAA,MAAA,QLqrFZ,4FK5rFI,4FAaI,iBAAA,QACA,MAAA,QLorFR,2GACA,iHKnsFI,2GAAA,iHAmBQ,iBAAA,QACA,MAAA,QLqrFZ,yFKzsFI,yFA6BQ,aAAA,QLirFZ,8GK9sFI,8GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,QLkrFZ,sHKrtFI,sHAuCQ,MAAA,kBLmrFZ,4HK1tFI,4HA0CU,MAAA,QLqrFd,4GK/tFI,4GAgDM,aAAA,QC9DN,wGDKM,aAAA,QCLN,yGDUI,aAAA,QLmvFR,oGACA,0GAFA,mGAFA,oGACA,0GK7uFI,mGAOQ,OAAA,IAAA,MAAA,QL8uFZ,2FKrvFI,2FAaI,iBAAA,QACA,MAAA,QL6uFR,0GACA,gHK5vFI,0GAAA,gHAmBQ,iBAAA,QACA,MAAA,QL8uFZ,wFKlwFI,wFA6BQ,aAAA,QL0uFZ,6GKvwFI,6GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,QL2uFZ,qHK9wFI,qHAuCQ,MAAA,kBL4uFZ,2HKnxFI,2HA0CU,MAAA,QL8uFd,2GKxxFI,2GAgDM,aAAA,QC9DN,2GDKM,aAAA,QCLN,4GDUI,aAAA,QL4yFR,uGACA,6GAFA,sGAFA,uGACA,6GKtyFI,sGAOQ,OAAA,IAAA,MAAA,QLuyFZ,8FK9yFI,8FAaI,iBAAA,QACA,MAAA,QLsyFR,6GACA,mHKrzFI,6GAAA,mHAmBQ,iBAAA,QACA,MAAA,QLuyFZ,2FK3zFI,2FA6BQ,aAAA,QLmyFZ,gHKh0FI,gHAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,QLoyFZ,wHKv0FI,wHAuCQ,MAAA,kBLqyFZ,8HK50FI,8HA0CU,MAAA,QLuyFd,8GKj1FI,8GAgDM,aAAA,QC9DN,0GDKM,aAAA,QCLN,2GDUI,aAAA,QLq2FR,sGACA,4GAFA,qGAFA,sGACA,4GK/1FI,qGAOQ,OAAA,IAAA,MAAA,QLg2FZ,6FKv2FI,6FAaI,iBAAA,QACA,MAAA,QL+1FR,4GACA,kHK92FI,4GAAA,kHAmBQ,iBAAA,QACA,MAAA,KLg2FZ,0FKp3FI,0FA6BQ,aAAA,QL41FZ,+GKz3FI,+GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,QL61FZ,uHKh4FI,uHAuCQ,MAAA,kBL81FZ,6HKr4FI,6HA0CU,MAAA,QLg2Fd,6GK14FI,6GAgDM,aAAA,QC9DN,wGDKM,aAAA,QCLN,yGDUI,aAAA,QL85FR,oGACA,0GAFA,mGAFA,oGACA,0GKx5FI,mGAOQ,OAAA,IAAA,MAAA,QLy5FZ,2FKh6FI,2FAaI,iBAAA,QACA,MAAA,KLw5FR,0GACA,gHKv6FI,0GAAA,gHAmBQ,iBAAA,QACA,MAAA,KLy5FZ,wFK76FI,wFA6BQ,aAAA,QLq5FZ,6GKl7FI,6GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLs5FZ,qHKz7FI,qHAuCQ,MAAA,qBLu5FZ,2HK97FI,2HA0CU,MAAA,KLy5Fd,2GKn8FI,2GAgDM,aAAA,QC9DN,0GDKM,aAAA,QCLN,2GDUI,aAAA,QLu9FR,sGACA,4GAFA,qGAFA,sGACA,4GKj9FI,qGAOQ,OAAA,IAAA,MAAA,QLk9FZ,6FKz9FI,6FAaI,iBAAA,QACA,MAAA,KLi9FR,4GACA,kHKh+FI,4GAAA,kHAmBQ,iBAAA,QACA,MAAA,KLk9FZ,0FKt+FI,0FA6BQ,aAAA,QL88FZ,+GK3+FI,+GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KL+8FZ,uHKl/FI,uHAuCQ,MAAA,qBLg9FZ,6HKv/FI,6HA0CU,MAAA,KLk9Fd,6GK5/FI,6GAgDM,aAAA,QC9DN,0GDKM,aAAA,QCLN,2GDUI,aAAA,QLghGR,sGACA,4GAFA,qGAFA,sGACA,4GK1gGI,qGAOQ,OAAA,IAAA,MAAA,QL2gGZ,6FKlhGI,6FAaI,iBAAA,QACA,MAAA,KL0gGR,4GACA,kHKzhGI,4GAAA,kHAmBQ,iBAAA,QACA,MAAA,KL2gGZ,0FK/hGI,0FA6BQ,aAAA,QLugGZ,+GKpiGI,+GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLwgGZ,uHK3iGI,uHAuCQ,MAAA,qBLygGZ,6HKhjGI,6HA0CU,MAAA,KL2gGd,6GKrjGI,6GAgDM,aAAA,QC9DN,wGDKM,aAAA,QCLN,yGDUI,aAAA,QLykGR,oGACA,0GAFA,mGAFA,oGACA,0GKnkGI,mGAOQ,OAAA,IAAA,MAAA,QLokGZ,2FK3kGI,2FAaI,iBAAA,QACA,MAAA,KLmkGR,0GACA,gHKllGI,0GAAA,gHAmBQ,iBAAA,QACA,MAAA,KLokGZ,wFKxlGI,wFA6BQ,aAAA,QLgkGZ,6GK7lGI,6GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLikGZ,qHKpmGI,qHAuCQ,MAAA,qBLkkGZ,2HKzmGI,2HA0CU,MAAA,KLokGd,2GK9mGI,2GAgDM,aAAA,QC9DN,uGDKM,aAAA,QCLN,wGDUI,aAAA,QLkoGR,mGACA,yGAFA,kGAFA,mGACA,yGK5nGI,kGAOQ,OAAA,IAAA,MAAA,QL6nGZ,0FKpoGI,0FAaI,iBAAA,QACA,MAAA,KL4nGR,yGACA,+GK3oGI,yGAAA,+GAmBQ,iBAAA,QACA,MAAA,KL6nGZ,uFKjpGI,uFA6BQ,aAAA,QLynGZ,4GKtpGI,4GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KL0nGZ,oHK7pGI,oHAuCQ,MAAA,qBL2nGZ,0HKlqGI,0HA0CU,MAAA,KL6nGd,0GKvqGI,0GAgDM,aAAA,QC9DN,0GDKM,aAAA,QCLN,2GDUI,aAAA,QL2rGR,sGACA,4GAFA,qGAFA,sGACA,4GKrrGI,qGAOQ,OAAA,IAAA,MAAA,QLsrGZ,6FK7rGI,6FAaI,iBAAA,QACA,MAAA,QLqrGR,4GACA,kHKpsGI,4GAAA,kHAmBQ,iBAAA,QACA,MAAA,KLsrGZ,0FK1sGI,0FA6BQ,aAAA,QLkrGZ,+GK/sGI,+GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,QLmrGZ,uHKttGI,uHAuCQ,MAAA,kBLorGZ,6HK3tGI,6HA0CU,MAAA,QLsrGd,6GKhuGI,6GAgDM,aAAA,QC9DN,0GDKM,aAAA,QCLN,2GDUI,aAAA,QLovGR,sGACA,4GAFA,qGAFA,sGACA,4GK9uGI,qGAOQ,OAAA,IAAA,MAAA,QL+uGZ,6FKtvGI,6FAaI,iBAAA,QACA,MAAA,QL8uGR,4GACA,kHK7vGI,4GAAA,kHAmBQ,iBAAA,QACA,MAAA,QL+uGZ,0FKnwGI,0FA6BQ,aAAA,QL2uGZ,+GKxwGI,+GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,QL4uGZ,uHK/wGI,uHAuCQ,MAAA,kBL6uGZ,6HKpxGI,6HA0CU,MAAA,QL+uGd,6GKzxGI,6GAgDM,aAAA,QC9DN,yGDKM,aAAA,QCLN,0GDUI,aAAA,QL6yGR,qGACA,2GAFA,oGAFA,qGACA,2GKvyGI,oGAOQ,OAAA,IAAA,MAAA,QLwyGZ,4FK/yGI,4FAaI,iBAAA,QACA,MAAA,KLuyGR,2GACA,iHKtzGI,2GAAA,iHAmBQ,iBAAA,QACA,MAAA,KLwyGZ,yFK5zGI,yFA6BQ,aAAA,QLoyGZ,8GKj0GI,8GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLqyGZ,sHKx0GI,sHAuCQ,MAAA,qBLsyGZ,4HK70GI,4HA0CU,MAAA,KLwyGd,4GKl1GI,4GAgDM,aAAA,QC9DN,wGDKM,aAAA,QCLN,yGDUI,aAAA,QLs2GR,oGACA,0GAFA,mGAFA,oGACA,0GKh2GI,mGAOQ,OAAA,IAAA,MAAA,QLi2GZ,2FKx2GI,2FAaI,iBAAA,QACA,MAAA,KLg2GR,0GACA,gHK/2GI,0GAAA,gHAmBQ,iBAAA,QACA,MAAA,KLi2GZ,wFKr3GI,wFA6BQ,aAAA,QL61GZ,6GK13GI,6GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KL81GZ,qHKj4GI,qHAuCQ,MAAA,qBL+1GZ,2HKt4GI,2HA0CU,MAAA,KLi2Gd,2GK34GI,2GAgDM,aAAA,QC9DN,wGDKM,aAAA,QCLN,yGDUI,aAAA,QL+5GR,oGACA,0GAFA,mGAFA,oGACA,0GKz5GI,mGAOQ,OAAA,IAAA,MAAA,QL05GZ,2FKj6GI,2FAaI,iBAAA,QACA,MAAA,KLy5GR,0GACA,gHKx6GI,0GAAA,gHAmBQ,iBAAA,QACA,MAAA,KL05GZ,wFK96GI,wFA6BQ,aAAA,QLs5GZ,6GKn7GI,6GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLu5GZ,qHK17GI,qHAuCQ,MAAA,qBLw5GZ,2HK/7GI,2HA0CU,MAAA,KL05Gd,2GKp8GI,2GAgDM,aAAA,QC9DN,yGDKM,aAAA,KCLN,0GDUI,aAAA,KLw9GR,qGACA,2GAFA,oGAFA,qGACA,2GKl9GI,oGAOQ,OAAA,IAAA,MAAA,KLm9GZ,4FK19GI,4FAaI,iBAAA,KACA,MAAA,QLk9GR,2GACA,iHKj+GI,2GAAA,iHAmBQ,iBAAA,QACA,MAAA,QLm9GZ,yFKv+GI,yFA6BQ,aAAA,KL+8GZ,8GK5+GI,8GAiCQ,iBAAA,KACA,aAAA,QACA,MAAA,QLg9GZ,sHKn/GI,sHAuCQ,MAAA,kBLi9GZ,4HKx/GI,4HA0CU,MAAA,QLm9Gd,4GK7/GI,4GAgDM,aAAA,KC9DN,wGDKM,aAAA,QCLN,yGDUI,aAAA,QLihHR,oGACA,0GAFA,mGAFA,oGACA,0GK3gHI,mGAOQ,OAAA,IAAA,MAAA,QL4gHZ,2FKnhHI,2FAaI,iBAAA,QACA,MAAA,KL2gHR,0GACA,gHK1hHI,0GAAA,gHAmBQ,iBAAA,QACA,MAAA,KL4gHZ,wFKhiHI,wFA6BQ,aAAA,QLwgHZ,6GKriHI,6GAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLygHZ,qHK5iHI,qHAuCQ,MAAA,qBL0gHZ,2HKjjHI,2HA0CU,MAAA,KL4gHd,2GKtjHI,2GAgDM,aAAA,QC9DN,6GDKM,aAAA,QCLN,8GDUI,aAAA,QL0kHR,yGACA,+GAFA,wGAFA,yGACA,+GKpkHI,wGAOQ,OAAA,IAAA,MAAA,QLqkHZ,gGK5kHI,gGAaI,iBAAA,QACA,MAAA,KLokHR,+GACA,qHKnlHI,+GAAA,qHAmBQ,iBAAA,QACA,MAAA,KLqkHZ,6FKzlHI,6FA6BQ,aAAA,QLikHZ,kHK9lHI,kHAiCQ,iBAAA,QACA,aAAA,QACA,MAAA,KLkkHZ,0HKrmHI,0HAuCQ,MAAA,qBLmkHZ,gIK1mHI,gIA0CU,MAAA,KLqkHd,gHK/mHI,gHAgDM,aAAA,QE/DV,oBACE,QAAA,GAIF,wBAEI,OAAA,KAFJ,0BAKI,MAAA,KAMF,0CAEI,WAAA,QAFJ,4CAEI,WAAA,QAFJ,0CAEI,WAAA,QAFJ,uCAEI,WAAA,QAFJ,0CAEI,WAAA,QAFJ,yCAEI,WAAA,QAFJ,wCAEI,WAAA,QAFJ,uCAEI,WAAA,QAMJ,4CAEI,WAAA,QAFJ,uCAEI,WAAA,QAFJ,wCAEI,WAAA,QAFJ,uCAEI,WAAA,QAFJ,0CAEI,WAAA,QAFJ,yCAEI,WAAA,QAFJ,uCAEI,WAAA,QAFJ,yCAEI,WAAA,QAFJ,yCAEI,WAAA,QAFJ,uCAEI,WAAA,QAFJ,sCAEI,WAAA,QAFJ,yCAEI,WAAA,QAFJ,yCAEI,WAAA,QAFJ,wCAEI,WAAA,QAFJ,uCAEI,WAAA,QAFJ,uCAEI,WAAA,QAFJ,wCAEI,WAAA,KAFJ,uCAEI,WAAA,QAFJ,4CAEI,WAAA,QDzBF,yBCgCA,iBAAA,QACA,iBAAA,KDjCA,qDCuCI,WAAA,QDvCJ,uDCuCI,WAAA,QDvCJ,qDCuCI,WAAA,QDvCJ,kDCuCI,WAAA,QDvCJ,qDCuCI,WAAA,QDvCJ,oDCuCI,WAAA,QDvCJ,mDCuCI,WAAA,QDvCJ,kDCuCI,WAAA,QDvCJ,uDC+CI,WAAA,QD/CJ,kDC+CI,WAAA,QD/CJ,mDC+CI,WAAA,QD/CJ,kDC+CI,WAAA,QD/CJ,qDC+CI,WAAA,QD/CJ,oDC+CI,WAAA,QD/CJ,kDC+CI,WAAA,QD/CJ,oDC+CI,WAAA,QD/CJ,oDC+CI,WAAA,QD/CJ,kDC+CI,WAAA,QD/CJ,iDC+CI,WAAA,QD/CJ,oDC+CI,WAAA,QD/CJ,oDC+CI,WAAA,QD/CJ,mDC+CI,WAAA,QD/CJ,kDC+CI,WAAA,QD/CJ,kDC+CI,WAAA,QD/CJ,mDC+CI,WAAA,KD/CJ,kDC+CI,WAAA,QD/CJ,uDC+CI,WAAA,QP4zHR,sGQ32HE,mFAEE,aAAA,QR82HJ,sGQ32HE,mFAEE,aAAA,QR82HJ,2EQ32HE,wDAEE,iBAAA,QACA,aAAA,QR82HJ,wGQ33HE,qFAEE,aAAA,QR83HJ,wGQ33HE,qFAEE,aAAA,QR83HJ,6EQ33HE,0DAEE,iBAAA,QACA,aAAA,QR83HJ,sGQ34HE,mFAEE,aAAA,QR84HJ,sGQ34HE,mFAEE,aAAA,QR84HJ,2EQ34HE,wDAEE,iBAAA,QACA,aAAA,QR84HJ,mGQ35HE,gFAEE,aAAA,QR85HJ,mGQ35HE,gFAEE,aAAA,QR85HJ,wEQ35HE,qDAEE,iBAAA,QACA,aAAA,QR85HJ,sGQ36HE,mFAEE,aAAA,QR86HJ,sGQ36HE,mFAEE,aAAA,QR86HJ,2EQ36HE,wDAEE,iBAAA,QACA,aAAA,QR86HJ,qGQ37HE,kFAEE,aAAA,QR87HJ,qGQ37HE,kFAEE,aAAA,QR87HJ,0EQ37HE,uDAEE,iBAAA,QACA,aAAA,QR87HJ,oGQ38HE,iFAEE,aAAA,QR88HJ,oGQ38HE,iFAEE,aAAA,QR88HJ,yEQ38HE,sDAEE,iBAAA,QACA,aAAA,QR88HJ,mGQ39HE,gFAEE,aAAA,QR89HJ,mGQ39HE,gFAEE,aAAA,QR89HJ,wEQ39HE,qDAEE,iBAAA,QACA,aAAA,QR89HJ,wGQx9HE,qFAEE,aAAA,QR29HJ,wGQx9HE,qFAEE,aAAA,QR29HJ,6EQx9HE,0DAEE,iBAAA,QACA,aAAA,QR29HJ,mGQx+HE,gFAEE,aAAA,QR2+HJ,mGQx+HE,gFAEE,aAAA,QR2+HJ,wEQx+HE,qDAEE,iBAAA,QACA,aAAA,QR2+HJ,oGQx/HE,iFAEE,aAAA,QR2/HJ,oGQx/HE,iFAEE,aAAA,QR2/HJ,yEQx/HE,sDAEE,iBAAA,QACA,aAAA,QR2/HJ,mGQxgIE,gFAEE,aAAA,QR2gIJ,mGQxgIE,gFAEE,aAAA,QR2gIJ,wEQxgIE,qDAEE,iBAAA,QACA,aAAA,QR2gIJ,sGQxhIE,mFAEE,aAAA,QR2hIJ,sGQxhIE,mFAEE,aAAA,QR2hIJ,2EQxhIE,wDAEE,iBAAA,QACA,aAAA,QR2hIJ,qGQxiIE,kFAEE,aAAA,QR2iIJ,qGQxiIE,kFAEE,aAAA,QR2iIJ,0EQxiIE,uDAEE,iBAAA,QACA,aAAA,QR2iIJ,mGQxjIE,gFAEE,aAAA,QR2jIJ,mGQxjIE,gFAEE,aAAA,QR2jIJ,wEQxjIE,qDAEE,iBAAA,QACA,aAAA,QR2jIJ,qGQxkIE,kFAEE,aAAA,QR2kIJ,qGQxkIE,kFAEE,aAAA,QR2kIJ,0EQxkIE,uDAEE,iBAAA,QACA,aAAA,QR2kIJ,qGQxlIE,kFAEE,aAAA,QR2lIJ,qGQxlIE,kFAEE,aAAA,QR2lIJ,0EQxlIE,uDAEE,iBAAA,QACA,aAAA,QR2lIJ,mGQxmIE,gFAEE,aAAA,QR2mIJ,mGQxmIE,gFAEE,aAAA,QR2mIJ,wEQxmIE,qDAEE,iBAAA,QACA,aAAA,QR2mIJ,kGQxnIE,+EAEE,aAAA,QR2nIJ,kGQxnIE,+EAEE,aAAA,QR2nIJ,uEQxnIE,oDAEE,iBAAA,QACA,aAAA,QR2nIJ,qGQxoIE,kFAEE,aAAA,QR2oIJ,qGQxoIE,kFAEE,aAAA,QR2oIJ,0EQxoIE,uDAEE,iBAAA,QACA,aAAA,QR2oIJ,qGQxpIE,kFAEE,aAAA,QR2pIJ,qGQxpIE,kFAEE,aAAA,QR2pIJ,0EQxpIE,uDAEE,iBAAA,QACA,aAAA,QR2pIJ,oGQxqIE,iFAEE,aAAA,QR2qIJ,oGQxqIE,iFAEE,aAAA,QR2qIJ,yEQxqIE,sDAEE,iBAAA,QACA,aAAA,QR2qIJ,mGQxrIE,gFAEE,aAAA,QR2rIJ,mGQxrIE,gFAEE,aAAA,QR2rIJ,wEQxrIE,qDAEE,iBAAA,QACA,aAAA,QR2rIJ,mGQxsIE,gFAEE,aAAA,QR2sIJ,mGQxsIE,gFAEE,aAAA,QR2sIJ,wEQxsIE,qDAEE,iBAAA,QACA,aAAA,QR2sIJ,oGQxtIE,iFAEE,aAAA,KR2tIJ,oGQxtIE,iFAEE,aAAA,KR2tIJ,yEQxtIE,sDAEE,iBAAA,KACA,aAAA,KR2tIJ,mGQxuIE,gFAEE,aAAA,QR2uIJ,mGQxuIE,gFAEE,aAAA,QR2uIJ,wEQxuIE,qDAEE,iBAAA,QACA,aAAA,QR2uIJ,wGQxvIE,qFAEE,aAAA,QR2vIJ,wGQxvIE,qFAEE,aAAA,QR2vIJ,6EQxvIE,0DAEE,iBAAA,QACA,aAAA,QFhCA,6FN2xIJ,0EQnvIM,aAAA,QRwvIN,iHMhyII,8FE+CE,aAAA,QRsvIN,iHMryII,8FEoDE,aAAA,QRsvIN,sFM1yII,mEEyDE,iBAAA,QACA,aAAA,QRsvIN,mHMhzII,gGE+CE,aAAA,QRswIN,mHMrzII,gGEoDE,aAAA,QRswIN,wFM1zII,qEEyDE,iBAAA,QACA,aAAA,QRswIN,iHMh0II,8FE+CE,aAAA,QRsxIN,iHMr0II,8FEoDE,aAAA,QRsxIN,sFM10II,mEEyDE,iBAAA,QACA,aAAA,QRsxIN,8GMh1II,2FE+CE,aAAA,QRsyIN,8GMr1II,2FEoDE,aAAA,QRsyIN,mFM11II,gEEyDE,iBAAA,QACA,aAAA,QRsyIN,iHMh2II,8FE+CE,aAAA,QRszIN,iHMr2II,8FEoDE,aAAA,QRszIN,sFM12II,mEEyDE,iBAAA,QACA,aAAA,QRszIN,gHMh3II,6FE+CE,aAAA,QRs0IN,gHMr3II,6FEoDE,aAAA,QRs0IN,qFM13II,kEEyDE,iBAAA,QACA,aAAA,QRs0IN,+GMh4II,4FE+CE,aAAA,QRs1IN,+GMr4II,4FEoDE,aAAA,QRs1IN,oFM14II,iEEyDE,iBAAA,QACA,aAAA,QRs1IN,8GMh5II,2FE+CE,aAAA,QRs2IN,8GMr5II,2FEoDE,aAAA,QRs2IN,mFM15II,gEEyDE,iBAAA,QACA,aAAA,QRs2IN,mHMh6II,gGEkEE,aAAA,QRm2IN,mHMr6II,gGEuEE,aAAA,QRm2IN,wFM16II,qEE4EE,iBAAA,QACA,aAAA,QRm2IN,8GMh7II,2FEkEE,aAAA,QRm3IN,8GMr7II,2FEuEE,aAAA,QRm3IN,mFM17II,gEE4EE,iBAAA,QACA,aAAA,QRm3IN,+GMh8II,4FEkEE,aAAA,QRm4IN,+GMr8II,4FEuEE,aAAA,QRm4IN,oFM18II,iEE4EE,iBAAA,QACA,aAAA,QRm4IN,8GMh9II,2FEkEE,aAAA,QRm5IN,8GMr9II,2FEuEE,aAAA,QRm5IN,mFM19II,gEE4EE,iBAAA,QACA,aAAA,QRm5IN,iHMh+II,8FEkEE,aAAA,QRm6IN,iHMr+II,8FEuEE,aAAA,QRm6IN,sFM1+II,mEE4EE,iBAAA,QACA,aAAA,QRm6IN,gHMh/II,6FEkEE,aAAA,QRm7IN,gHMr/II,6FEuEE,aAAA,QRm7IN,qFM1/II,kEE4EE,iBAAA,QACA,aAAA,QRm7IN,8GMhgJI,2FEkEE,aAAA,QRm8IN,8GMrgJI,2FEuEE,aAAA,QRm8IN,mFM1gJI,gEE4EE,iBAAA,QACA,aAAA,QRm8IN,gHMhhJI,6FEkEE,aAAA,QRm9IN,gHMrhJI,6FEuEE,aAAA,QRm9IN,qFM1hJI,kEE4EE,iBAAA,QACA,aAAA,QRm9IN,gHMhiJI,6FEkEE,aAAA,QRm+IN,gHMriJI,6FEuEE,aAAA,QRm+IN,qFM1iJI,kEE4EE,iBAAA,QACA,aAAA,QRm+IN,8GMhjJI,2FEkEE,aAAA,QRm/IN,8GMrjJI,2FEuEE,aAAA,QRm/IN,mFM1jJI,gEE4EE,iBAAA,QACA,aAAA,QRm/IN,6GMhkJI,0FEkEE,aAAA,QRmgJN,6GMrkJI,0FEuEE,aAAA,QRmgJN,kFM1kJI,+DE4EE,iBAAA,QACA,aAAA,QRmgJN,gHMhlJI,6FEkEE,aAAA,QRmhJN,gHMrlJI,6FEuEE,aAAA,QRmhJN,qFM1lJI,kEE4EE,iBAAA,QACA,aAAA,QRmhJN,gHMhmJI,6FEkEE,aAAA,QRmiJN,gHMrmJI,6FEuEE,aAAA,QRmiJN,qFM1mJI,kEE4EE,iBAAA,QACA,aAAA,QRmiJN,+GMhnJI,4FEkEE,aAAA,QRmjJN,+GMrnJI,4FEuEE,aAAA,QRmjJN,oFM1nJI,iEE4EE,iBAAA,QACA,aAAA,QRmjJN,8GMhoJI,2FEkEE,aAAA,QRmkJN,8GMroJI,2FEuEE,aAAA,QRmkJN,mFM1oJI,gEE4EE,iBAAA,QACA,aAAA,QRmkJN,8GMhpJI,2FEkEE,aAAA,QRmlJN,8GMrpJI,2FEuEE,aAAA,QRmlJN,mFM1pJI,gEE4EE,iBAAA,QACA,aAAA,QRmlJN,+GMhqJI,4FEkEE,aAAA,KRmmJN,+GMrqJI,4FEuEE,aAAA,KRmmJN,oFM1qJI,iEE4EE,iBAAA,KACA,aAAA,KRmmJN,8GMhrJI,2FEkEE,aAAA,QRmnJN,8GMrrJI,2FEuEE,aAAA,QRmnJN,mFM1rJI,gEE4EE,iBAAA,QACA,aAAA,QRmnJN,mHMhsJI,gGEkEE,aAAA,QRmoJN,mHMrsJI,gGEuEE,aAAA,QRmoJN,wFM1sJI,qEE4EE,iBAAA,QACA,aAAA,QC/EN,aAEI,SAAA,SAFJ,oBCHE,YAAA,iBAAA,CAAA,aAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,kBAEA,WAAA,OACA,YAAA,IACA,YAAA,IACA,WAAA,KACA,WAAA,MACA,gBAAA,KACA,YAAA,KACA,eAAA,KACA,eAAA,OACA,WAAA,OACA,aAAA,OACA,YAAA,OACA,WAAA,KCGE,cAAA,OCkKE,UAAA,QHvKF,iBAAA,KACA,MAAA,KACA,QAAA,MACA,UAAA,MACA,QAAA,OAAA,MACA,SAAA,SACA,WAAA,OACA,UAAA,WACA,QAAA,KAjBJ,kBAqBI,iBAAA,QACA,OAAA,IAAA,MAAA,QACA,QAAA,KACA,MAAA,MAxBJ,oBA4BI,iBAAA,QACA,OAAA,IAAA,MAAA,KACA,cAAA,OACA,MAAA,KACA,OAAA,QACA,YAAA,IACA,OAAA,KACA,KAAA,KACA,YAAA,KACA,aAAA,IACA,SAAA,SACA,WAAA,OACA,IAAA,EAEA,oBAAA,KAAA,iBAAA,KAAA,gBAAA,KAAA,YAAA,KACA,MAAA,KA3CJ,0BAAA,2BAAA,0BAgDM,iBAAA,QACA,MAAA,QAjDN,mBAsDI,YAAA,KACA,IAAA,KAvDJ,gBA2DI,IAAA,KA3DJ,iBA+DI,IAAA,KI9DJ,eb+xJA,gBa7xJE,iBAAA,QACA,OAAA,IAAA,MAAA,KACA,cAAA,OACA,MAAA,KACA,OAAA,KACA,MAAA,KACA,QAAA,IAAA,IARF,qBAAA,sBAAA,qBb4yJA,sBADA,uBADA,sBa7xJI,iBAAA,QACA,MAAA,QCdJ,uBAEI,aAAA,qBACA,MAAA,QAHJ,0BAOI,aAAA,qBACA,MAAA,QARJ,wBAYI,aAAA,qBACA,MAAA,QAbJ,2BAiBI,aAAA,qBACA,MAAA,QAlBJ,0BAsBI,aAAA,qBACA,MAAA,QAvBJ,8CA0BM,aAAA,qBA1BN,sDA8BM,iBAAA,QR7BF,wBQoCA,iBAAA,QACA,MAAA,QRrCA,uCNu1JJ,qCc9yJM,MAAA,QCfN,wBAGI,iBAAA,QAHJ,gCAOI,iBAAA,QAPJ,8BAWI,iBAAA,QAXJ,6BAeI,iBAAA,QAfJ,gCAmBI,iBAAA,QAKJ,gCf8zJA,6Be5zJE,UAAA,QCtDF,MACE,QAAA,KADF,qBAII,QAAA,KAJJ,qBAQI,QAAA,KAMF,mCAGM,WAAA,QAKN,gCAEI,WAAA,KAFJ,+CAKM,WAAA,QALN,+CASM,iBAAA,8JAKN,sDAGM,MAAA,mBAKN,0CAGM,WAAA,QAKN,yCAEI,OAAA,MACA,MAAA,KAHJ,iDAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,yCAeI,aAAA,QAfJ,gDAAA,iDAmBM,aAAA,QAKN,iDAGM,WAAA,mBACA,MAAA,KAKN,gDAGM,aAAA,QAAA,YAAA,YAHN,wDAOM,aAAA,QAAA,YAAA,YAKN,kCAEI,WAAA,KACA,aAAA,QAHJ,iDAMM,WAAA,QAKN,6BAEI,MAAA,QAIJ,oDAGM,WAAA,QAHN,2DhB41JF,4DgBp1JQ,OAAA,IAAA,MAAA,KARN,4DAaQ,mBAAA,mBACA,kBAAA,mBAdR,2DAkBQ,iBAAA,mBACA,oBAAA,mBAKR,6CAGM,iBAAA,mBAKN,yCAGM,WAAA,QAHN,+CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,yCAWM,iBAAA,QACA,kBAAA,QAKN,+CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,+CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,2CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,2CAQM,iBAAA,oEACA,OAAA,KAKN,4CAEI,MAAA,QAnLJ,qCAGM,WAAA,QAKN,kCAEI,WAAA,KAFJ,iDAKM,WAAA,QALN,iDASM,iBAAA,8JAKN,wDAGM,MAAA,qBAKN,4CAGM,WAAA,QAKN,2CAEI,OAAA,MACA,MAAA,KAHJ,mDAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,2CAeI,aAAA,QAfJ,kDAAA,mDAmBM,aAAA,QAKN,mDAGM,WAAA,qBACA,MAAA,KAKN,kDAGM,aAAA,QAAA,YAAA,YAHN,0DAOM,aAAA,QAAA,YAAA,YAKN,oCAEI,WAAA,KACA,aAAA,QAHJ,mDAMM,WAAA,QAKN,+BAEI,MAAA,QAIJ,sDAGM,WAAA,QAHN,6DhB+9JF,8DgBv9JQ,OAAA,IAAA,MAAA,KARN,8DAaQ,mBAAA,qBACA,kBAAA,qBAdR,6DAkBQ,iBAAA,qBACA,oBAAA,qBAKR,+CAGM,iBAAA,qBAKN,2CAGM,WAAA,QAHN,iDAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,2CAWM,iBAAA,QACA,kBAAA,QAKN,iDAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,iDASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,6CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,6CAQM,iBAAA,oEACA,OAAA,KAKN,8CAEI,MAAA,QAnLJ,mCAGM,WAAA,QAKN,gCAEI,WAAA,KAFJ,+CAKM,WAAA,QALN,+CASM,iBAAA,8JAKN,sDAGM,MAAA,mBAKN,0CAGM,WAAA,QAKN,yCAEI,OAAA,MACA,MAAA,KAHJ,iDAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,yCAeI,aAAA,QAfJ,gDAAA,iDAmBM,aAAA,QAKN,iDAGM,WAAA,mBACA,MAAA,KAKN,gDAGM,aAAA,QAAA,YAAA,YAHN,wDAOM,aAAA,QAAA,YAAA,YAKN,kCAEI,WAAA,KACA,aAAA,QAHJ,iDAMM,WAAA,QAKN,6BAEI,MAAA,QAIJ,oDAGM,WAAA,QAHN,2DhBkmKF,4DgB1lKQ,OAAA,IAAA,MAAA,KARN,4DAaQ,mBAAA,mBACA,kBAAA,mBAdR,2DAkBQ,iBAAA,mBACA,oBAAA,mBAKR,6CAGM,iBAAA,mBAKN,yCAGM,WAAA,QAHN,+CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,yCAWM,iBAAA,QACA,kBAAA,QAKN,+CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,+CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,2CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,2CAQM,iBAAA,oEACA,OAAA,KAKN,4CAEI,MAAA,QAnLJ,gCAGM,WAAA,QAKN,6BAEI,WAAA,KAFJ,4CAKM,WAAA,QALN,4CASM,iBAAA,8JAKN,mDAGM,MAAA,oBAKN,uCAGM,WAAA,QAKN,sCAEI,OAAA,MACA,MAAA,KAHJ,8CAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,sCAeI,aAAA,QAfJ,6CAAA,8CAmBM,aAAA,QAKN,8CAGM,WAAA,oBACA,MAAA,KAKN,6CAGM,aAAA,QAAA,YAAA,YAHN,qDAOM,aAAA,QAAA,YAAA,YAKN,+BAEI,WAAA,KACA,aAAA,QAHJ,8CAMM,WAAA,QAKN,0BAEI,MAAA,QAIJ,iDAGM,WAAA,QAHN,wDhBquKF,yDgB7tKQ,OAAA,IAAA,MAAA,KARN,yDAaQ,mBAAA,oBACA,kBAAA,oBAdR,wDAkBQ,iBAAA,oBACA,oBAAA,oBAKR,0CAGM,iBAAA,oBAKN,sCAGM,WAAA,QAHN,4CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,sCAWM,iBAAA,QACA,kBAAA,QAKN,4CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,4CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,wCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,wCAQM,iBAAA,oEACA,OAAA,KAKN,yCAEI,MAAA,QAnLJ,mCAGM,WAAA,QAKN,gCAEI,WAAA,QAFJ,+CAKM,WAAA,QALN,+CASM,iBAAA,qJAKN,sDAGM,MAAA,mBAKN,0CAGM,WAAA,QAKN,yCAEI,OAAA,MACA,MAAA,KAHJ,iDAMM,WAAA,QACA,MAAA,QACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,yCAeI,aAAA,QAfJ,gDAAA,iDAmBM,aAAA,QAKN,iDAGM,WAAA,mBACA,MAAA,QAKN,gDAGM,aAAA,QAAA,YAAA,YAHN,wDAOM,aAAA,QAAA,YAAA,YAKN,kCAEI,WAAA,QACA,aAAA,QAHJ,iDAMM,WAAA,QAKN,6BAEI,MAAA,QAIJ,oDAGM,WAAA,QAHN,2DhBw2KF,4DgBh2KQ,OAAA,IAAA,MAAA,QARN,4DAaQ,mBAAA,mBACA,kBAAA,mBAdR,2DAkBQ,iBAAA,mBACA,oBAAA,mBAKR,6CAGM,iBAAA,mBAKN,yCAGM,WAAA,QAHN,+CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,yCAWM,iBAAA,QACA,kBAAA,QAKN,+CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,OAAA,CAAA,MAAA,EAAA,QALN,+CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QAKN,2CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,iBAAA,CAAA,MAAA,EAAA,IAAA,kBAJN,2CAQM,iBAAA,8DACA,OAAA,KAKN,4CAEI,MAAA,QAnLJ,kCAGM,WAAA,QAKN,+BAEI,WAAA,KAFJ,8CAKM,WAAA,QALN,8CASM,iBAAA,8JAKN,qDAGM,MAAA,mBAKN,yCAGM,WAAA,QAKN,wCAEI,OAAA,MACA,MAAA,KAHJ,gDAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,wCAeI,aAAA,QAfJ,+CAAA,gDAmBM,aAAA,QAKN,gDAGM,WAAA,mBACA,MAAA,KAKN,+CAGM,aAAA,QAAA,YAAA,YAHN,uDAOM,aAAA,QAAA,YAAA,YAKN,iCAEI,WAAA,KACA,aAAA,QAHJ,gDAMM,WAAA,QAKN,4BAEI,MAAA,QAIJ,mDAGM,WAAA,QAHN,0DhB2+KF,2DgBn+KQ,OAAA,IAAA,MAAA,KARN,2DAaQ,mBAAA,mBACA,kBAAA,mBAdR,0DAkBQ,iBAAA,mBACA,oBAAA,mBAKR,4CAGM,iBAAA,mBAKN,wCAGM,WAAA,QAHN,8CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,wCAWM,iBAAA,QACA,kBAAA,QAKN,8CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,8CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,0CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,0CAQM,iBAAA,oEACA,OAAA,KAKN,2CAEI,MAAA,QAnLJ,iCAGM,WAAA,QAKN,8BAEI,WAAA,QAFJ,6CAKM,WAAA,QALN,6CASM,iBAAA,qJAKN,oDAGM,MAAA,qBAKN,wCAGM,WAAA,QAKN,uCAEI,OAAA,MACA,MAAA,KAHJ,+CAMM,WAAA,QACA,MAAA,QACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,uCAeI,aAAA,QAfJ,8CAAA,+CAmBM,aAAA,QAKN,+CAGM,WAAA,qBACA,MAAA,QAKN,8CAGM,aAAA,QAAA,YAAA,YAHN,sDAOM,aAAA,QAAA,YAAA,YAKN,gCAEI,WAAA,QACA,aAAA,QAHJ,+CAMM,WAAA,QAKN,2BAEI,MAAA,QAIJ,kDAGM,WAAA,QAHN,yDhB8mLF,0DgBtmLQ,OAAA,IAAA,MAAA,QARN,0DAaQ,mBAAA,qBACA,kBAAA,qBAdR,yDAkBQ,iBAAA,qBACA,oBAAA,qBAKR,2CAGM,iBAAA,qBAKN,uCAGM,WAAA,QAHN,6CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,uCAWM,iBAAA,QACA,kBAAA,QAKN,6CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,OAAA,CAAA,MAAA,EAAA,QALN,6CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QAKN,yCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,iBAAA,CAAA,MAAA,EAAA,IAAA,kBAJN,yCAQM,iBAAA,8DACA,OAAA,KAKN,0CAEI,MAAA,QAnLJ,gCAGM,WAAA,QAKN,6BAEI,WAAA,KAFJ,4CAKM,WAAA,QALN,4CASM,iBAAA,8JAKN,mDAGM,MAAA,kBAKN,uCAGM,WAAA,QAKN,sCAEI,OAAA,MACA,MAAA,KAHJ,8CAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,sCAeI,aAAA,QAfJ,6CAAA,8CAmBM,aAAA,QAKN,8CAGM,WAAA,kBACA,MAAA,KAKN,6CAGM,aAAA,QAAA,YAAA,YAHN,qDAOM,aAAA,QAAA,YAAA,YAKN,+BAEI,WAAA,KACA,aAAA,QAHJ,8CAMM,WAAA,QAKN,0BAEI,MAAA,QAIJ,iDAGM,WAAA,QAHN,wDhBivLF,yDgBzuLQ,OAAA,IAAA,MAAA,KARN,yDAaQ,mBAAA,kBACA,kBAAA,kBAdR,wDAkBQ,iBAAA,kBACA,oBAAA,kBAKR,0CAGM,iBAAA,kBAKN,sCAGM,WAAA,QAHN,4CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,sCAWM,iBAAA,QACA,kBAAA,QAKN,4CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,4CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,wCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,wCAQM,iBAAA,oEACA,OAAA,KAKN,yCAEI,MAAA,QAnLJ,qCAGM,WAAA,QAKN,kCAEI,WAAA,KAFJ,iDAKM,WAAA,QALN,iDASM,iBAAA,8JAKN,wDAGM,MAAA,oBAKN,4CAGM,WAAA,QAKN,2CAEI,OAAA,MACA,MAAA,KAHJ,mDAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,2CAeI,aAAA,QAfJ,kDAAA,mDAmBM,aAAA,QAKN,mDAGM,WAAA,oBACA,MAAA,KAKN,kDAGM,aAAA,QAAA,YAAA,YAHN,0DAOM,aAAA,QAAA,YAAA,YAKN,oCAEI,WAAA,KACA,aAAA,QAHJ,mDAMM,WAAA,QAKN,+BAEI,MAAA,QAIJ,sDAGM,WAAA,QAHN,6DhBo3LF,8DgB52LQ,OAAA,IAAA,MAAA,KARN,8DAaQ,mBAAA,oBACA,kBAAA,oBAdR,6DAkBQ,iBAAA,oBACA,oBAAA,oBAKR,+CAGM,iBAAA,oBAKN,2CAGM,WAAA,QAHN,iDAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,2CAWM,iBAAA,QACA,kBAAA,QAKN,iDAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,iDASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,6CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,6CAQM,iBAAA,oEACA,OAAA,KAKN,8CAEI,MAAA,QAnLJ,gCAGM,WAAA,QAKN,6BAEI,WAAA,KAFJ,4CAKM,WAAA,QALN,4CASM,iBAAA,8JAKN,mDAGM,MAAA,iBAKN,uCAGM,WAAA,QAKN,sCAEI,OAAA,MACA,MAAA,KAHJ,8CAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,sCAeI,aAAA,QAfJ,6CAAA,8CAmBM,aAAA,QAKN,8CAGM,WAAA,iBACA,MAAA,KAKN,6CAGM,aAAA,QAAA,YAAA,YAHN,qDAOM,aAAA,QAAA,YAAA,YAKN,+BAEI,WAAA,KACA,aAAA,QAHJ,8CAMM,WAAA,QAKN,0BAEI,MAAA,QAIJ,iDAGM,WAAA,QAHN,wDhBu/LF,yDgB/+LQ,OAAA,IAAA,MAAA,KARN,yDAaQ,mBAAA,iBACA,kBAAA,iBAdR,wDAkBQ,iBAAA,iBACA,oBAAA,iBAKR,0CAGM,iBAAA,iBAKN,sCAGM,WAAA,QAHN,4CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,sCAWM,iBAAA,QACA,kBAAA,QAKN,4CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,4CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,wCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,wCAQM,iBAAA,oEACA,OAAA,KAKN,yCAEI,MAAA,QAnLJ,iCAGM,WAAA,QAKN,8BAEI,WAAA,KAFJ,6CAKM,WAAA,QALN,6CASM,iBAAA,8JAKN,oDAGM,MAAA,oBAKN,wCAGM,WAAA,QAKN,uCAEI,OAAA,MACA,MAAA,KAHJ,+CAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,uCAeI,aAAA,QAfJ,8CAAA,+CAmBM,aAAA,QAKN,+CAGM,WAAA,oBACA,MAAA,KAKN,8CAGM,aAAA,QAAA,YAAA,YAHN,sDAOM,aAAA,QAAA,YAAA,YAKN,gCAEI,WAAA,KACA,aAAA,QAHJ,+CAMM,WAAA,QAKN,2BAEI,MAAA,QAIJ,kDAGM,WAAA,QAHN,yDhB0nMF,0DgBlnMQ,OAAA,IAAA,MAAA,KARN,0DAaQ,mBAAA,oBACA,kBAAA,oBAdR,yDAkBQ,iBAAA,oBACA,oBAAA,oBAKR,2CAGM,iBAAA,oBAKN,uCAGM,WAAA,QAHN,6CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,uCAWM,iBAAA,QACA,kBAAA,QAKN,6CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,6CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,yCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,yCAQM,iBAAA,oEACA,OAAA,KAKN,0CAEI,MAAA,QAnLJ,gCAGM,WAAA,QAKN,6BAEI,WAAA,QAFJ,4CAKM,WAAA,QALN,4CASM,iBAAA,qJAKN,mDAGM,MAAA,mBAKN,uCAGM,WAAA,QAKN,sCAEI,OAAA,MACA,MAAA,KAHJ,8CAMM,WAAA,QACA,MAAA,QACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,sCAeI,aAAA,QAfJ,6CAAA,8CAmBM,aAAA,QAKN,8CAGM,WAAA,mBACA,MAAA,QAKN,6CAGM,aAAA,QAAA,YAAA,YAHN,qDAOM,aAAA,QAAA,YAAA,YAKN,+BAEI,WAAA,QACA,aAAA,QAHJ,8CAMM,WAAA,QAKN,0BAEI,MAAA,QAIJ,iDAGM,WAAA,QAHN,wDhB6vMF,yDgBrvMQ,OAAA,IAAA,MAAA,QARN,yDAaQ,mBAAA,mBACA,kBAAA,mBAdR,wDAkBQ,iBAAA,mBACA,oBAAA,mBAKR,0CAGM,iBAAA,mBAKN,sCAGM,WAAA,QAHN,4CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,sCAWM,iBAAA,QACA,kBAAA,QAKN,4CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,OAAA,CAAA,MAAA,EAAA,QALN,4CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QAKN,wCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,iBAAA,CAAA,MAAA,EAAA,IAAA,kBAJN,wCAQM,iBAAA,8DACA,OAAA,KAKN,yCAEI,MAAA,QAnLJ,mCAGM,WAAA,QAKN,gCAEI,WAAA,KAFJ,+CAKM,WAAA,QALN,+CASM,iBAAA,8JAKN,sDAGM,MAAA,oBAKN,0CAGM,WAAA,QAKN,yCAEI,OAAA,MACA,MAAA,KAHJ,iDAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,yCAeI,aAAA,QAfJ,gDAAA,iDAmBM,aAAA,QAKN,iDAGM,WAAA,oBACA,MAAA,KAKN,gDAGM,aAAA,QAAA,YAAA,YAHN,wDAOM,aAAA,QAAA,YAAA,YAKN,kCAEI,WAAA,KACA,aAAA,QAHJ,iDAMM,WAAA,QAKN,6BAEI,MAAA,QAIJ,oDAGM,WAAA,QAHN,2DhBg4MF,4DgBx3MQ,OAAA,IAAA,MAAA,KARN,4DAaQ,mBAAA,oBACA,kBAAA,oBAdR,2DAkBQ,iBAAA,oBACA,oBAAA,oBAKR,6CAGM,iBAAA,oBAKN,yCAGM,WAAA,QAHN,+CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,yCAWM,iBAAA,QACA,kBAAA,QAKN,+CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,+CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,2CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,2CAQM,iBAAA,oEACA,OAAA,KAKN,4CAEI,MAAA,QAnLJ,kCAGM,WAAA,QAKN,+BAEI,WAAA,KAFJ,8CAKM,WAAA,QALN,8CASM,iBAAA,8JAKN,qDAGM,MAAA,mBAKN,yCAGM,WAAA,QAKN,wCAEI,OAAA,MACA,MAAA,KAHJ,gDAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,wCAeI,aAAA,QAfJ,+CAAA,gDAmBM,aAAA,QAKN,gDAGM,WAAA,mBACA,MAAA,KAKN,+CAGM,aAAA,QAAA,YAAA,YAHN,uDAOM,aAAA,QAAA,YAAA,YAKN,iCAEI,WAAA,KACA,aAAA,QAHJ,gDAMM,WAAA,QAKN,4BAEI,MAAA,QAIJ,mDAGM,WAAA,QAHN,0DhBmgNF,2DgB3/MQ,OAAA,IAAA,MAAA,KARN,2DAaQ,mBAAA,mBACA,kBAAA,mBAdR,0DAkBQ,iBAAA,mBACA,oBAAA,mBAKR,4CAGM,iBAAA,mBAKN,wCAGM,WAAA,QAHN,8CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,wCAWM,iBAAA,QACA,kBAAA,QAKN,8CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,8CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,0CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,0CAQM,iBAAA,oEACA,OAAA,KAKN,2CAEI,MAAA,QAnLJ,gCAGM,WAAA,QAKN,6BAEI,WAAA,KAFJ,4CAKM,WAAA,QALN,4CASM,iBAAA,8JAKN,mDAGM,MAAA,mBAKN,uCAGM,WAAA,QAKN,sCAEI,OAAA,MACA,MAAA,KAHJ,8CAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,sCAeI,aAAA,QAfJ,6CAAA,8CAmBM,aAAA,QAKN,8CAGM,WAAA,mBACA,MAAA,KAKN,6CAGM,aAAA,QAAA,YAAA,YAHN,qDAOM,aAAA,QAAA,YAAA,YAKN,+BAEI,WAAA,KACA,aAAA,QAHJ,8CAMM,WAAA,QAKN,0BAEI,MAAA,QAIJ,iDAGM,WAAA,QAHN,wDhBsoNF,yDgB9nNQ,OAAA,IAAA,MAAA,KARN,yDAaQ,mBAAA,mBACA,kBAAA,mBAdR,wDAkBQ,iBAAA,mBACA,oBAAA,mBAKR,0CAGM,iBAAA,mBAKN,sCAGM,WAAA,QAHN,4CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,sCAWM,iBAAA,QACA,kBAAA,QAKN,4CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,4CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,wCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,wCAQM,iBAAA,oEACA,OAAA,KAKN,yCAEI,MAAA,QAnLJ,kCAGM,WAAA,QAKN,+BAEI,WAAA,KAFJ,8CAKM,WAAA,QALN,8CASM,iBAAA,8JAKN,qDAGM,MAAA,oBAKN,yCAGM,WAAA,QAKN,wCAEI,OAAA,MACA,MAAA,KAHJ,gDAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,wCAeI,aAAA,QAfJ,+CAAA,gDAmBM,aAAA,QAKN,gDAGM,WAAA,oBACA,MAAA,KAKN,+CAGM,aAAA,QAAA,YAAA,YAHN,uDAOM,aAAA,QAAA,YAAA,YAKN,iCAEI,WAAA,KACA,aAAA,QAHJ,gDAMM,WAAA,QAKN,4BAEI,MAAA,QAIJ,mDAGM,WAAA,QAHN,0DhBywNF,2DgBjwNQ,OAAA,IAAA,MAAA,KARN,2DAaQ,mBAAA,oBACA,kBAAA,oBAdR,0DAkBQ,iBAAA,oBACA,oBAAA,oBAKR,4CAGM,iBAAA,oBAKN,wCAGM,WAAA,QAHN,8CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,wCAWM,iBAAA,QACA,kBAAA,QAKN,8CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,8CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,0CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,0CAQM,iBAAA,oEACA,OAAA,KAKN,2CAEI,MAAA,QAnLJ,kCAGM,WAAA,QAKN,+BAEI,WAAA,KAFJ,8CAKM,WAAA,QALN,8CASM,iBAAA,8JAKN,qDAGM,MAAA,oBAKN,yCAGM,WAAA,QAKN,wCAEI,OAAA,MACA,MAAA,KAHJ,gDAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,wCAeI,aAAA,QAfJ,+CAAA,gDAmBM,aAAA,QAKN,gDAGM,WAAA,oBACA,MAAA,KAKN,+CAGM,aAAA,QAAA,YAAA,YAHN,uDAOM,aAAA,QAAA,YAAA,YAKN,iCAEI,WAAA,KACA,aAAA,QAHJ,gDAMM,WAAA,QAKN,4BAEI,MAAA,QAIJ,mDAGM,WAAA,QAHN,0DhB44NF,2DgBp4NQ,OAAA,IAAA,MAAA,KARN,2DAaQ,mBAAA,oBACA,kBAAA,oBAdR,0DAkBQ,iBAAA,oBACA,oBAAA,oBAKR,4CAGM,iBAAA,oBAKN,wCAGM,WAAA,QAHN,8CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,wCAWM,iBAAA,QACA,kBAAA,QAKN,8CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,8CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,0CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,0CAQM,iBAAA,oEACA,OAAA,KAKN,2CAEI,MAAA,QAnLJ,gCAGM,WAAA,QAKN,6BAEI,WAAA,KAFJ,4CAKM,WAAA,QALN,4CASM,iBAAA,8JAKN,mDAGM,MAAA,oBAKN,uCAGM,WAAA,QAKN,sCAEI,OAAA,MACA,MAAA,KAHJ,8CAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,sCAeI,aAAA,QAfJ,6CAAA,8CAmBM,aAAA,QAKN,8CAGM,WAAA,oBACA,MAAA,KAKN,6CAGM,aAAA,QAAA,YAAA,YAHN,qDAOM,aAAA,QAAA,YAAA,YAKN,+BAEI,WAAA,KACA,aAAA,QAHJ,8CAMM,WAAA,QAKN,0BAEI,MAAA,QAIJ,iDAGM,WAAA,QAHN,wDhB+gOF,yDgBvgOQ,OAAA,IAAA,MAAA,KARN,yDAaQ,mBAAA,oBACA,kBAAA,oBAdR,wDAkBQ,iBAAA,oBACA,oBAAA,oBAKR,0CAGM,iBAAA,oBAKN,sCAGM,WAAA,QAHN,4CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,sCAWM,iBAAA,QACA,kBAAA,QAKN,4CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,4CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,wCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,wCAQM,iBAAA,oEACA,OAAA,KAKN,yCAEI,MAAA,QAnLJ,+BAGM,WAAA,QAKN,4BAEI,WAAA,KAFJ,2CAKM,WAAA,QALN,2CASM,iBAAA,8JAKN,kDAGM,MAAA,mBAKN,sCAGM,WAAA,QAKN,qCAEI,OAAA,MACA,MAAA,KAHJ,6CAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,qCAeI,aAAA,QAfJ,4CAAA,6CAmBM,aAAA,QAKN,6CAGM,WAAA,mBACA,MAAA,KAKN,4CAGM,aAAA,QAAA,YAAA,YAHN,oDAOM,aAAA,QAAA,YAAA,YAKN,8BAEI,WAAA,KACA,aAAA,QAHJ,6CAMM,WAAA,QAKN,yBAEI,MAAA,QAIJ,gDAGM,WAAA,QAHN,uDhBkpOF,wDgB1oOQ,OAAA,IAAA,MAAA,KARN,wDAaQ,mBAAA,mBACA,kBAAA,mBAdR,uDAkBQ,iBAAA,mBACA,oBAAA,mBAKR,yCAGM,iBAAA,mBAKN,qCAGM,WAAA,QAHN,2CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,qCAWM,iBAAA,QACA,kBAAA,QAKN,2CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,2CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,uCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,uCAQM,iBAAA,oEACA,OAAA,KAKN,wCAEI,MAAA,QAnLJ,kCAGM,WAAA,QAKN,+BAEI,WAAA,QAFJ,8CAKM,WAAA,QALN,8CASM,iBAAA,qJAKN,qDAGM,MAAA,oBAKN,yCAGM,WAAA,QAKN,wCAEI,OAAA,MACA,MAAA,KAHJ,gDAMM,WAAA,QACA,MAAA,QACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,wCAeI,aAAA,QAfJ,+CAAA,gDAmBM,aAAA,QAKN,gDAGM,WAAA,oBACA,MAAA,QAKN,+CAGM,aAAA,QAAA,YAAA,YAHN,uDAOM,aAAA,QAAA,YAAA,YAKN,iCAEI,WAAA,QACA,aAAA,QAHJ,gDAMM,WAAA,QAKN,4BAEI,MAAA,QAIJ,mDAGM,WAAA,QAHN,0DhBqxOF,2DgB7wOQ,OAAA,IAAA,MAAA,QARN,2DAaQ,mBAAA,oBACA,kBAAA,oBAdR,0DAkBQ,iBAAA,oBACA,oBAAA,oBAKR,4CAGM,iBAAA,oBAKN,wCAGM,WAAA,QAHN,8CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,wCAWM,iBAAA,QACA,kBAAA,QAKN,8CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,OAAA,CAAA,MAAA,EAAA,QALN,8CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QAKN,0CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,iBAAA,CAAA,MAAA,EAAA,IAAA,kBAJN,0CAQM,iBAAA,8DACA,OAAA,KAKN,2CAEI,MAAA,QAnLJ,kCAGM,WAAA,QAKN,+BAEI,WAAA,QAFJ,8CAKM,WAAA,QALN,8CASM,iBAAA,qJAKN,qDAGM,MAAA,mBAKN,yCAGM,WAAA,QAKN,wCAEI,OAAA,MACA,MAAA,KAHJ,gDAMM,WAAA,QACA,MAAA,QACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,wCAeI,aAAA,QAfJ,+CAAA,gDAmBM,aAAA,QAKN,gDAGM,WAAA,mBACA,MAAA,QAKN,+CAGM,aAAA,QAAA,YAAA,YAHN,uDAOM,aAAA,QAAA,YAAA,YAKN,iCAEI,WAAA,QACA,aAAA,QAHJ,gDAMM,WAAA,QAKN,4BAEI,MAAA,QAIJ,mDAGM,WAAA,QAHN,0DhBw5OF,2DgBh5OQ,OAAA,IAAA,MAAA,QARN,2DAaQ,mBAAA,mBACA,kBAAA,mBAdR,0DAkBQ,iBAAA,mBACA,oBAAA,mBAKR,4CAGM,iBAAA,mBAKN,wCAGM,WAAA,QAHN,8CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,wCAWM,iBAAA,QACA,kBAAA,QAKN,8CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,OAAA,CAAA,MAAA,EAAA,QALN,8CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QAKN,0CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,iBAAA,CAAA,MAAA,EAAA,IAAA,kBAJN,0CAQM,iBAAA,8DACA,OAAA,KAKN,2CAEI,MAAA,QAnLJ,iCAGM,WAAA,QAKN,8BAEI,WAAA,KAFJ,6CAKM,WAAA,QALN,6CASM,iBAAA,8JAKN,oDAGM,MAAA,mBAKN,wCAGM,WAAA,QAKN,uCAEI,OAAA,MACA,MAAA,KAHJ,+CAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,uCAeI,aAAA,QAfJ,8CAAA,+CAmBM,aAAA,QAKN,+CAGM,WAAA,mBACA,MAAA,KAKN,8CAGM,aAAA,QAAA,YAAA,YAHN,sDAOM,aAAA,QAAA,YAAA,YAKN,gCAEI,WAAA,KACA,aAAA,QAHJ,+CAMM,WAAA,QAKN,2BAEI,MAAA,QAIJ,kDAGM,WAAA,QAHN,yDhB2hPF,0DgBnhPQ,OAAA,IAAA,MAAA,KARN,0DAaQ,mBAAA,mBACA,kBAAA,mBAdR,yDAkBQ,iBAAA,mBACA,oBAAA,mBAKR,2CAGM,iBAAA,mBAKN,uCAGM,WAAA,QAHN,6CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,uCAWM,iBAAA,QACA,kBAAA,QAKN,6CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,6CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,yCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,yCAQM,iBAAA,oEACA,OAAA,KAKN,0CAEI,MAAA,QAnLJ,gCAGM,WAAA,QAKN,6BAEI,WAAA,KAFJ,4CAKM,WAAA,QALN,4CASM,iBAAA,8JAKN,mDAGM,MAAA,oBAKN,uCAGM,WAAA,QAKN,sCAEI,OAAA,MACA,MAAA,KAHJ,8CAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,sCAeI,aAAA,QAfJ,6CAAA,8CAmBM,aAAA,QAKN,8CAGM,WAAA,oBACA,MAAA,KAKN,6CAGM,aAAA,QAAA,YAAA,YAHN,qDAOM,aAAA,QAAA,YAAA,YAKN,+BAEI,WAAA,KACA,aAAA,QAHJ,8CAMM,WAAA,QAKN,0BAEI,MAAA,QAIJ,iDAGM,WAAA,QAHN,wDhB8pPF,yDgBtpPQ,OAAA,IAAA,MAAA,KARN,yDAaQ,mBAAA,oBACA,kBAAA,oBAdR,wDAkBQ,iBAAA,oBACA,oBAAA,oBAKR,0CAGM,iBAAA,oBAKN,sCAGM,WAAA,QAHN,4CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,sCAWM,iBAAA,QACA,kBAAA,QAKN,4CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,4CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,wCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,wCAQM,iBAAA,oEACA,OAAA,KAKN,yCAEI,MAAA,QAnLJ,gCAGM,WAAA,QAKN,6BAEI,WAAA,KAFJ,4CAKM,WAAA,QALN,4CASM,iBAAA,8JAKN,mDAGM,MAAA,oBAKN,uCAGM,WAAA,QAKN,sCAEI,OAAA,MACA,MAAA,KAHJ,8CAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,sCAeI,aAAA,QAfJ,6CAAA,8CAmBM,aAAA,QAKN,8CAGM,WAAA,oBACA,MAAA,KAKN,6CAGM,aAAA,QAAA,YAAA,YAHN,qDAOM,aAAA,QAAA,YAAA,YAKN,+BAEI,WAAA,KACA,aAAA,QAHJ,8CAMM,WAAA,QAKN,0BAEI,MAAA,QAIJ,iDAGM,WAAA,QAHN,wDhBiyPF,yDgBzxPQ,OAAA,IAAA,MAAA,KARN,yDAaQ,mBAAA,oBACA,kBAAA,oBAdR,wDAkBQ,iBAAA,oBACA,oBAAA,oBAKR,0CAGM,iBAAA,oBAKN,sCAGM,WAAA,QAHN,4CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,sCAWM,iBAAA,QACA,kBAAA,QAKN,4CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,4CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,wCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,wCAQM,iBAAA,oEACA,OAAA,KAKN,yCAEI,MAAA,QAnLJ,iCAGM,WAAA,KAKN,8BAEI,WAAA,QAFJ,6CAKM,WAAA,KALN,6CASM,iBAAA,qJAKN,oDAGM,MAAA,qBAKN,wCAGM,WAAA,KAKN,uCAEI,OAAA,MACA,MAAA,KAHJ,+CAMM,WAAA,KACA,MAAA,QACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,uCAeI,aAAA,KAfJ,8CAAA,+CAmBM,aAAA,KAKN,+CAGM,WAAA,qBACA,MAAA,QAKN,8CAGM,aAAA,KAAA,YAAA,YAHN,sDAOM,aAAA,KAAA,YAAA,YAKN,gCAEI,WAAA,QACA,aAAA,KAHJ,+CAMM,WAAA,KAKN,2BAEI,MAAA,KAIJ,kDAGM,WAAA,KAHN,yDhBo6PF,0DgB55PQ,OAAA,IAAA,MAAA,QARN,0DAaQ,mBAAA,qBACA,kBAAA,qBAdR,yDAkBQ,iBAAA,qBACA,oBAAA,qBAKR,2CAGM,iBAAA,qBAKN,uCAGM,WAAA,KAHN,6CAOM,WAAA,EAAA,EAAA,KAAA,IAAA,CAAA,EAAA,EAAA,IAAA,KAPN,uCAWM,iBAAA,KACA,kBAAA,KAKN,6CAGM,WAAA,KACA,MAAA,KACA,WAAA,MAAA,EAAA,OAAA,CAAA,MAAA,EAAA,QALN,6CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,IAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QAKN,yCAGM,iBAAA,KACA,WAAA,MAAA,KAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KAAA,IAAA,CAAA,MAAA,EAAA,IAAA,iBAAA,CAAA,MAAA,EAAA,IAAA,kBAJN,yCAQM,iBAAA,8DACA,OAAA,KAKN,0CAEI,MAAA,KAnLJ,gCAGM,WAAA,QAKN,6BAEI,WAAA,KAFJ,4CAKM,WAAA,QALN,4CASM,iBAAA,8JAKN,mDAGM,MAAA,qBAKN,uCAGM,WAAA,QAKN,sCAEI,OAAA,MACA,MAAA,KAHJ,8CAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,sCAeI,aAAA,QAfJ,6CAAA,8CAmBM,aAAA,QAKN,8CAGM,WAAA,qBACA,MAAA,KAKN,6CAGM,aAAA,QAAA,YAAA,YAHN,qDAOM,aAAA,QAAA,YAAA,YAKN,+BAEI,WAAA,KACA,aAAA,QAHJ,8CAMM,WAAA,QAKN,0BAEI,MAAA,QAIJ,iDAGM,WAAA,QAHN,wDhBuiQF,yDgB/hQQ,OAAA,IAAA,MAAA,KARN,yDAaQ,mBAAA,qBACA,kBAAA,qBAdR,wDAkBQ,iBAAA,qBACA,oBAAA,qBAKR,0CAGM,iBAAA,qBAKN,sCAGM,WAAA,QAHN,4CAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,sCAWM,iBAAA,QACA,kBAAA,QAKN,4CAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,4CASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,wCAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,wCAQM,iBAAA,oEACA,OAAA,KAKN,yCAEI,MAAA,QAnLJ,qCAGM,WAAA,QAKN,kCAEI,WAAA,KAFJ,iDAKM,WAAA,QALN,iDASM,iBAAA,8JAKN,wDAGM,MAAA,kBAKN,4CAGM,WAAA,QAKN,2CAEI,OAAA,MACA,MAAA,KAHJ,mDAMM,WAAA,QACA,MAAA,KACA,UAAA,MACA,YAAA,MACA,YAAA,IAVN,2CAeI,aAAA,QAfJ,kDAAA,mDAmBM,aAAA,QAKN,mDAGM,WAAA,kBACA,MAAA,KAKN,kDAGM,aAAA,QAAA,YAAA,YAHN,0DAOM,aAAA,QAAA,YAAA,YAKN,oCAEI,WAAA,KACA,aAAA,QAHJ,mDAMM,WAAA,QAKN,+BAEI,MAAA,QAIJ,sDAGM,WAAA,QAHN,6DhB0qQF,8DgBlqQQ,OAAA,IAAA,MAAA,KARN,8DAaQ,mBAAA,kBACA,kBAAA,kBAdR,6DAkBQ,iBAAA,kBACA,oBAAA,kBAKR,+CAGM,iBAAA,kBAKN,2CAGM,WAAA,QAHN,iDAOM,WAAA,EAAA,EAAA,KAAA,OAAA,CAAA,EAAA,EAAA,IAAA,QAPN,2CAWM,iBAAA,QACA,kBAAA,QAKN,iDAGM,WAAA,QACA,MAAA,QACA,WAAA,MAAA,EAAA,IAAA,CAAA,MAAA,EAAA,KALN,iDASM,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,KAKN,6CAGM,iBAAA,QACA,WAAA,MAAA,KAAA,EAAA,OAAA,CAAA,MAAA,EAAA,KAAA,OAAA,CAAA,MAAA,EAAA,IAAA,oBAAA,CAAA,MAAA,EAAA,IAAA,qBAJN,6CAQM,iBAAA,oEACA,OAAA,KAKN,8CAEI,MAAA,QCzLN,kBACE,OAAA,IAAA,MAAA,QACA,cAAA,OACA,OAAA,QACA,UAAA,IACA,QAAA,aACA,YAAA,MACA,SAAA,OACA,SAAA,SACA,WAAA,KACA,WAAA,aAAA,YAAA,IAAA,CAAA,WAAA,YAAA,KACA,oBAAA,KAAA,iBAAA,KAAA,gBAAA,KAAA,YAAA,KACA,eAAA,OACA,QAAA,EAbF,8CAgBI,cAAA,OACA,QAAA,aACA,IAAA,EACA,kBAAA,mBAAA,UAAA,mBAnBJ,+BAwBI,WAAA,EAAA,EAAA,EAAA,MAAA,oBjB21QJ,+CiBn3QA,8CjBo3QA,0CiBt1QI,WAAA,WACA,OAAA,QACA,QAAA,WACA,UAAA,KACA,YAAA,IACA,YAAA,OACA,QAAA,OAAA,MACA,eAAA,OjB21QJ,+CiBh4QA,8CA0CI,WAAA,OACA,QAAA,EjB21QJ,wEiBt4QA,uEA8CM,WAAA,QACA,MAAA,QjB61QN,wEiB54QA,uEAoDQ,WAAA,QACA,MAAA,KjB61QR,0EiBl5QA,yEAoDQ,WAAA,QACA,MAAA,KjBm2QR,wEiBx5QA,uEAoDQ,WAAA,QACA,MAAA,KjBy2QR,qEiB95QA,oEAoDQ,WAAA,QACA,MAAA,KjB+2QR,wEiBp6QA,uEAoDQ,WAAA,QACA,MAAA,QjBq3QR,uEiB16QA,sEAoDQ,WAAA,QACA,MAAA,KjB23QR,sEiBh7QA,qEAoDQ,WAAA,QACA,MAAA,QjBi4QR,qEiBt7QA,oEAoDQ,WAAA,QACA,MAAA,KjBu4QR,0EiB57QA,yEA2DQ,WAAA,QACA,MAAA,KjBs4QR,qEiBl8QA,oEA2DQ,WAAA,QACA,MAAA,KjB44QR,sEiBx8QA,qEA2DQ,WAAA,QACA,MAAA,KjBk5QR,qEiB98QA,oEA2DQ,WAAA,QACA,MAAA,QjBw5QR,wEiBp9QA,uEA2DQ,WAAA,QACA,MAAA,KjB85QR,uEiB19QA,sEA2DQ,WAAA,QACA,MAAA,KjBo6QR,qEiBh+QA,oEA2DQ,WAAA,QACA,MAAA,KjB06QR,uEiBt+QA,sEA2DQ,WAAA,QACA,MAAA,KjBg7QR,uEiB5+QA,sEA2DQ,WAAA,QACA,MAAA,KjBs7QR,qEiBl/QA,oEA2DQ,WAAA,QACA,MAAA,KjB47QR,oEiBx/QA,mEA2DQ,WAAA,QACA,MAAA,KjBk8QR,uEiB9/QA,sEA2DQ,WAAA,QACA,MAAA,QjBw8QR,uEiBpgRA,sEA2DQ,WAAA,QACA,MAAA,QjB88QR,sEiB1gRA,qEA2DQ,WAAA,QACA,MAAA,KjBo9QR,qEiBhhRA,oEA2DQ,WAAA,QACA,MAAA,KjB09QR,qEiBthRA,oEA2DQ,WAAA,QACA,MAAA,KjBg+QR,sEiB5hRA,qEA2DQ,WAAA,KACA,MAAA,QjBs+QR,qEiBliRA,oEA2DQ,WAAA,QACA,MAAA,KjB4+QR,0EiBxiRA,yEA2DQ,WAAA,QACA,MAAA,KA5DR,8CAkEI,0BAAA,MACA,uBAAA,MAnEJ,+CAuEI,2BAAA,MACA,wBAAA,MjBg/QJ,uCiBxjRA,oCA8EI,KAAA,EACA,OAAA,EACA,QAAA,EACA,SAAA,SACA,IAAA,EACA,WAAA,OACA,QAAA,GjBg/QJ,qEiBpkRA,oEjBqkRA,gEiB1+QM,UAAA,QACA,YAAA,IACA,QAAA,MAAA,MjB++QN,sEiB5kRA,qEjB6kRA,iEiBx+QM,UAAA,QACA,YAAA,IACA,QAAA,MAAA,MjB6+QN,sEiBplRA,qEjBqlRA,iEiBt+QM,UAAA,QACA,YAAA,aACA,QAAA,MAAA,MAjHN,4CAAA,iDAAA,4CAwHI,OAAA,QjBw+QJ,yEiBhmRA,wEjBimRA,oEAGA,8EADqE,6EAErE,yEAHA,yEADqE,wEAErE,oEiBt+QM,OAAA,QAEA,QAAA,GA/HN,uEAoII,WAAA,YAAA,IApIJ,uEAyIM,cAAA,EAAA,MAAA,MAAA,EAzIN,wEA6IM,cAAA,MAAA,EAAA,EAAA,MjB2+QN,wFiBxnRA,8DAyJI,2BAAA,MACA,wBAAA,MjBo+QJ,uFiB9nRA,+DA+JI,0BAAA,MACA,uBAAA,MXtKA,6BW4KA,aAAA,QX5KA,mFN8oRJ,kFiB99QM,iBAAA,QACA,MAAA,KACA,aAAA,QjBm+QN,mFMrpRI,kFWwLM,WAAA,QACA,MAAA,KjBk+QV,qFM3pRI,oFWwLM,WAAA,QACA,MAAA,KjBw+QV,mFMjqRI,kFWwLM,WAAA,QACA,MAAA,KjB8+QV,gFMvqRI,+EWwLM,WAAA,QACA,MAAA,KjBo/QV,mFM7qRI,kFWwLM,WAAA,QACA,MAAA,QjB0/QV,kFMnrRI,iFWwLM,WAAA,QACA,MAAA,KjBggRV,iFMzrRI,gFWwLM,WAAA,QACA,MAAA,QjBsgRV,gFM/rRI,+EWwLM,WAAA,QACA,MAAA,KjB4gRV,qFMrsRI,oFW+LM,WAAA,QACA,MAAA,QjB2gRV,gFM3sRI,+EW+LM,WAAA,QACA,MAAA,KjBihRV,iFMjtRI,gFW+LM,WAAA,QACA,MAAA,QjBuhRV,gFMvtRI,+EW+LM,WAAA,QACA,MAAA,QjB6hRV,mFM7tRI,kFW+LM,WAAA,QACA,MAAA,QjBmiRV,kFMnuRI,iFW+LM,WAAA,QACA,MAAA,QjByiRV,gFMzuRI,+EW+LM,WAAA,QACA,MAAA,KjB+iRV,kFM/uRI,iFW+LM,WAAA,QACA,MAAA,KjBqjRV,kFMrvRI,iFW+LM,WAAA,QACA,MAAA,KjB2jRV,gFM3vRI,+EW+LM,WAAA,QACA,MAAA,KjBikRV,+EMjwRI,8EW+LM,WAAA,QACA,MAAA,KjBukRV,kFMvwRI,iFW+LM,WAAA,QACA,MAAA,QjB6kRV,kFM7wRI,iFW+LM,WAAA,QACA,MAAA,QjBmlRV,iFMnxRI,gFW+LM,WAAA,QACA,MAAA,KjBylRV,gFMzxRI,+EW+LM,WAAA,QACA,MAAA,KjB+lRV,gFM/xRI,+EW+LM,WAAA,QACA,MAAA,KjBqmRV,iFMryRI,gFW+LM,WAAA,KACA,MAAA,QjB2mRV,gFM3yRI,+EW+LM,WAAA,QACA,MAAA,KjBinRV,qFMjzRI,oFW+LM,WAAA,QACA,MAAA,KXhMN,4BYJA,iBAAA,QACA,OAAA,QZGA,mCAAA,oCYCE,oBAAA,QZDF,+CNg0RJ,+CkB1zRM,iBAAA,QZNF,wCYSE,iBAAA,QACA,MAAA,KZVF,mCN40RJ,4CAFA,4CACA,8CkB1zRM,iBAAA,QACA,MAAA,KZlBF,6CYsBE,iBAAA,QZtBF,0DY0BE,aAAA,QZ1BF,+DAAA,gEY8BI,aAAA,QACA,YAAA,EZ/BJ,yCYoCE,aAAA,QZpCF,4CYwCE,iBAAA,QACA,aAAA,QlB+zRN,+CMx2RI,+CY6CI,MAAA,KZ7CJ,uDN62RJ,uDkB3zRQ,aAAA,KlBk0RR,8CMp3RI,8CNk3RJ,gDACA,gDkBzzRM,iBAAA,QACA,aAAA,QC3DN,YACE,OAAA,eACA,QAAA,cACA,MAAA,eAIF,mBACE,WAAA,MAGF,6BACE,OAAA,EACA,KAAA,cACA,OAAA,IACA,OAAA,KACA,SAAA,OACA,QAAA,EACA,SAAA,SACA,MAAA,IAGF,gBACE,WAAA,QACA,OAAA,IAAA,OAAA,QACA,cAAA,KAIF,OACE,SAAA,OACA,SAAA,Sb/BE,gCaqCA,iBAAA,QnB83RJ,wCMn6RI,yCN85RJ,2CACA,6BAA8B,+BAC9B,kDACA,2CACA,oCmBp3RI,aAAA,Qb9CA,kDNw6RJ,oCmBt3RI,iBAAA,QblDA,wCaqDA,iBAAA,QbrDA,6CawDA,iBAAA", "sourcesContent": ["/*!\n *   AdminLTE v3.2.0\n *     Only Plugins\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n\n// Bootstrap\n// ---------------------------------------------------\n@import \"~bootstrap/scss/functions\";\n@import \"../bootstrap-variables\";\n@import \"~bootstrap/scss/mixins\";\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import \"../variables\";\n@import \"../variables-alt\";\n@import \"../mixins\";\n\n@import \"plugins\";\n", "//\n// Mixins: Animation\n//\n\n\n@keyframes flipInX {\n  0% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n\n  100% {\n    transform: perspective(400px);\n  }\n}\n\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes shake {\n  0% {\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@keyframes wobble {\n  0% {\n    transform: none;\n  }\n\n  15% {\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n\n  30% {\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n\n  45% {\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n\n  60% {\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n\n  75% {\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n\n  100% {\n    transform: none;\n  }\n}\n\n//\n", "//\n// Plugin: Full Calendar\n//\n\n// Buttons\n.fc-button {\n  background: $gray-100;\n  background-image: none;\n  border-bottom-color: #ddd;\n  border-color: #ddd;\n  color: $gray-700;\n\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: #e9e9e9;\n  }\n}\n\n// Calendar title\n.fc-header-title h2 {\n  color: #666;\n  font-size: 15px;\n  line-height: 1.6em;\n  margin-left: 10px;\n}\n\n.fc-header-right {\n  padding-right: 10px;\n}\n\n.fc-header-left {\n  padding-left: 10px;\n}\n\n// Calendar table header cells\n.fc-widget-header {\n  background: #fafafa;\n}\n\n.fc-grid {\n  border: 0;\n  width: 100%;\n}\n\n.fc-widget-header:first-of-type,\n.fc-widget-content:first-of-type {\n  border-left: 0;\n  border-right: 0;\n}\n\n.fc-widget-header:last-of-type,\n.fc-widget-content:last-of-type {\n  border-right: 0;\n}\n\n.fc-toolbar,\n.fc-toolbar.fc-header-toolbar {\n  margin: 0;\n  padding: 1rem;\n}\n\n@include media-breakpoint-down(xs) {\n  .fc-toolbar {\n    flex-direction: column;\n\n    .fc-left {\n      order: 1;\n      margin-bottom: .5rem;\n    }\n\n    .fc-center {\n      order: 0;\n      margin-bottom: .375rem;\n    }\n\n    .fc-right {\n      order: 2;\n    }\n  }\n}\n\n.fc-day-number {\n  font-size: 20px;\n  font-weight: 300;\n  padding-right: 10px;\n}\n\n.fc-color-picker {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n\n  > li {\n    float: left;\n    font-size: 30px;\n    line-height: 30px;\n    margin-right: 5px;\n\n    .fa,\n    .fas,\n    .far,\n    .fab,\n    .fal,\n    .fad,\n    .svg-inline--fa,\n    .ion {\n      transition: transform linear .3s;\n\n      &:hover {\n        @include rotate(30deg);\n      }\n    }\n  }\n}\n\n#add-new-event {\n  transition: all linear .3s;\n}\n\n.external-event {\n  @include box-shadow($card-shadow);\n\n  border-radius: $border-radius;\n  cursor: move;\n  font-weight: 700;\n  margin-bottom: 4px;\n  padding: 5px 10px;\n\n  &:hover {\n    @include box-shadow(inset 0 0 90px rgba(0, 0, 0, 0.2));\n  }\n}\n", "/*!\n *   AdminLTE v3.2.0\n *     Only Plugins\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n@-webkit-keyframes flipInX {\n  0% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n  40% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n  60% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n  80% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n  100% {\n    -webkit-transform: perspective(400px);\n    transform: perspective(400px);\n  }\n}\n@keyframes flipInX {\n  0% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n  40% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n  60% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n  80% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n  100% {\n    -webkit-transform: perspective(400px);\n    transform: perspective(400px);\n  }\n}\n\n@-webkit-keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@-webkit-keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n@-webkit-keyframes shake {\n  0% {\n    -webkit-transform: translate(2px, 1px) rotate(0deg);\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    -webkit-transform: translate(-1px, -2px) rotate(-2deg);\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    -webkit-transform: translate(-3px, 0) rotate(3deg);\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    -webkit-transform: translate(0, 2px) rotate(0deg);\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    -webkit-transform: translate(1px, -1px) rotate(1deg);\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    -webkit-transform: translate(-1px, 2px) rotate(-1deg);\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    -webkit-transform: translate(-3px, 1px) rotate(0deg);\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    -webkit-transform: translate(2px, 1px) rotate(-2deg);\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    -webkit-transform: translate(-1px, -1px) rotate(4deg);\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    -webkit-transform: translate(2px, 2px) rotate(0deg);\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    -webkit-transform: translate(1px, -2px) rotate(-1deg);\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@keyframes shake {\n  0% {\n    -webkit-transform: translate(2px, 1px) rotate(0deg);\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    -webkit-transform: translate(-1px, -2px) rotate(-2deg);\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    -webkit-transform: translate(-3px, 0) rotate(3deg);\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    -webkit-transform: translate(0, 2px) rotate(0deg);\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    -webkit-transform: translate(1px, -1px) rotate(1deg);\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    -webkit-transform: translate(-1px, 2px) rotate(-1deg);\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    -webkit-transform: translate(-3px, 1px) rotate(0deg);\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    -webkit-transform: translate(2px, 1px) rotate(-2deg);\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    -webkit-transform: translate(-1px, -1px) rotate(4deg);\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    -webkit-transform: translate(2px, 2px) rotate(0deg);\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    -webkit-transform: translate(1px, -2px) rotate(-1deg);\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@-webkit-keyframes wobble {\n  0% {\n    -webkit-transform: none;\n    transform: none;\n  }\n  15% {\n    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n  30% {\n    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n  45% {\n    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n  60% {\n    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n  75% {\n    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n  100% {\n    -webkit-transform: none;\n    transform: none;\n  }\n}\n\n@keyframes wobble {\n  0% {\n    -webkit-transform: none;\n    transform: none;\n  }\n  15% {\n    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n  30% {\n    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n  45% {\n    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n  60% {\n    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n  75% {\n    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n  100% {\n    -webkit-transform: none;\n    transform: none;\n  }\n}\n\n.fc-button {\n  background: #f8f9fa;\n  background-image: none;\n  border-bottom-color: #ddd;\n  border-color: #ddd;\n  color: #495057;\n}\n\n.fc-button:hover, .fc-button:active, .fc-button.hover {\n  background-color: #e9e9e9;\n}\n\n.fc-header-title h2 {\n  color: #666;\n  font-size: 15px;\n  line-height: 1.6em;\n  margin-left: 10px;\n}\n\n.fc-header-right {\n  padding-right: 10px;\n}\n\n.fc-header-left {\n  padding-left: 10px;\n}\n\n.fc-widget-header {\n  background: #fafafa;\n}\n\n.fc-grid {\n  border: 0;\n  width: 100%;\n}\n\n.fc-widget-header:first-of-type,\n.fc-widget-content:first-of-type {\n  border-left: 0;\n  border-right: 0;\n}\n\n.fc-widget-header:last-of-type,\n.fc-widget-content:last-of-type {\n  border-right: 0;\n}\n\n.fc-toolbar,\n.fc-toolbar.fc-header-toolbar {\n  margin: 0;\n  padding: 1rem;\n}\n\n@media (max-width: 575.98px) {\n  .fc-toolbar {\n    -ms-flex-direction: column;\n    flex-direction: column;\n  }\n  .fc-toolbar .fc-left {\n    -ms-flex-order: 1;\n    order: 1;\n    margin-bottom: .5rem;\n  }\n  .fc-toolbar .fc-center {\n    -ms-flex-order: 0;\n    order: 0;\n    margin-bottom: .375rem;\n  }\n  .fc-toolbar .fc-right {\n    -ms-flex-order: 2;\n    order: 2;\n  }\n}\n\n.fc-day-number {\n  font-size: 20px;\n  font-weight: 300;\n  padding-right: 10px;\n}\n\n.fc-color-picker {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.fc-color-picker > li {\n  float: left;\n  font-size: 30px;\n  line-height: 30px;\n  margin-right: 5px;\n}\n\n.fc-color-picker > li .fa,\n.fc-color-picker > li .fas,\n.fc-color-picker > li .far,\n.fc-color-picker > li .fab,\n.fc-color-picker > li .fal,\n.fc-color-picker > li .fad,\n.fc-color-picker > li .svg-inline--fa,\n.fc-color-picker > li .ion {\n  transition: -webkit-transform linear .3s;\n  transition: transform linear .3s;\n  transition: transform linear .3s, -webkit-transform linear .3s;\n}\n\n.fc-color-picker > li .fa:hover,\n.fc-color-picker > li .fas:hover,\n.fc-color-picker > li .far:hover,\n.fc-color-picker > li .fab:hover,\n.fc-color-picker > li .fal:hover,\n.fc-color-picker > li .fad:hover,\n.fc-color-picker > li .svg-inline--fa:hover,\n.fc-color-picker > li .ion:hover {\n  -webkit-transform: rotate(30deg);\n  transform: rotate(30deg);\n}\n\n#add-new-event {\n  transition: all linear .3s;\n}\n\n.external-event {\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  border-radius: 0.25rem;\n  cursor: move;\n  font-weight: 700;\n  margin-bottom: 4px;\n  padding: 5px 10px;\n}\n\n.external-event:hover {\n  box-shadow: inset 0 0 90px rgba(0, 0, 0, 0.2);\n}\n\n.select2-container--default .select2-selection--single {\n  border: 1px solid #ced4da;\n  padding: 0.46875rem 0.75rem;\n  height: calc(2.25rem + 2px);\n}\n\n.select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-dropdown {\n  border: 1px solid #ced4da;\n}\n\n.select2-container--default .select2-results__option {\n  padding: 6px 12px;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__rendered {\n  padding-left: 0;\n  height: auto;\n  margin-top: -3px;\n}\n\n.select2-container--default[dir=\"rtl\"] .select2-selection--single .select2-selection__rendered {\n  padding-right: 6px;\n  padding-left: 20px;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__arrow {\n  height: 31px;\n  right: 6px;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__arrow b {\n  margin-top: 0;\n}\n\n.select2-container--default .select2-dropdown .select2-search__field,\n.select2-container--default .select2-search--inline .select2-search__field {\n  border: 1px solid #ced4da;\n}\n\n.select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-search--inline .select2-search__field:focus {\n  outline: none;\n  border: 1px solid #80bdff;\n}\n\n.select2-container--default .select2-dropdown.select2-dropdown--below {\n  border-top: 0;\n}\n\n.select2-container--default .select2-dropdown.select2-dropdown--above {\n  border-bottom: 0;\n}\n\n.select2-container--default .select2-results__option[aria-disabled='true'] {\n  color: #6c757d;\n}\n\n.select2-container--default .select2-results__option[aria-selected='true'] {\n  background-color: #dee2e6;\n}\n\n.select2-container--default .select2-results__option[aria-selected='true'], .select2-container--default .select2-results__option[aria-selected='true']:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-results__option--highlighted {\n  background-color: #007bff;\n  color: #fff;\n}\n\n.select2-container--default .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #0074f0;\n  color: #fff;\n}\n\n.select2-container--default .select2-selection--multiple {\n  border: 1px solid #ced4da;\n  min-height: calc(2.25rem + 2px);\n}\n\n.select2-container--default .select2-selection--multiple:focus {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered {\n  padding: 0 0.375rem 0.375rem;\n  margin-bottom: -0.375rem;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline {\n  width: 100%;\n  margin-left: 0.375rem;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline .select2-search__field {\n  width: 100% !important;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field {\n  border: 0;\n  margin-top: 6px;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #007bff;\n  border-color: #006fe6;\n  color: #fff;\n  padding: 0 10px;\n  margin-top: .31rem;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n  float: right;\n  margin-left: 5px;\n  margin-right: -2px;\n}\n\n.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-search.select2-search--inline .select2-search__field, .select2-container--default .select2-selection--multiple.text-sm .select2-search.select2-search--inline .select2-search__field {\n  margin-top: 8px;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__choice, .select2-container--default .select2-selection--multiple.text-sm .select2-selection__choice {\n  margin-top: .4rem;\n}\n\n.select2-container--default.select2-container--focus .select2-selection--single,\n.select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #80bdff;\n}\n\n.select2-container--default.select2-container--focus .select2-search__field {\n  border: 0;\n}\n\n.select2-container--default .select2-selection--single .select2-selection__rendered li {\n  padding-right: 10px;\n}\n\n.input-group-prepend ~ .select2-container--default .select2-selection {\n  border-bottom-left-radius: 0;\n  border-top-left-radius: 0;\n}\n\n.input-group > .select2-container--default:not(:last-child) .select2-selection {\n  border-bottom-right-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.select2-container--bootstrap4.select2-container--focus .select2-selection {\n  box-shadow: none;\n}\n\nselect.form-control-sm ~ .select2-container--default {\n  font-size: 75%;\n}\n\n.text-sm .select2-container--default .select2-selection--single,\nselect.form-control-sm ~ .select2-container--default .select2-selection--single {\n  height: calc(1.8125rem + 2px);\n}\n\n.text-sm .select2-container--default .select2-selection--single .select2-selection__rendered,\nselect.form-control-sm ~ .select2-container--default .select2-selection--single .select2-selection__rendered {\n  margin-top: -.4rem;\n}\n\n.text-sm .select2-container--default .select2-selection--single .select2-selection__arrow,\nselect.form-control-sm ~ .select2-container--default .select2-selection--single .select2-selection__arrow {\n  top: -.12rem;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple {\n  min-height: calc(1.8125rem + 2px);\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered {\n  padding: 0 0.25rem 0.25rem;\n  margin-top: -0.1rem;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline {\n  margin-left: 0.25rem;\n}\n\n.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field,\nselect.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field {\n  margin-top: 6px;\n}\n\n.maximized-card .select2-dropdown {\n  z-index: 9999;\n}\n\n.select2-primary + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-primary + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-primary.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-primary .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-primary .select2-search--inline .select2-search__field:focus,\n.select2-primary .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-primary .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-primary .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #80bdff;\n}\n\n.select2-container--default .select2-primary .select2-results__option--highlighted,\n.select2-primary .select2-container--default .select2-results__option--highlighted {\n  background-color: #007bff;\n  color: #fff;\n}\n\n.select2-container--default .select2-primary .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-primary .select2-results__option--highlighted[aria-selected]:hover,\n.select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #0074f0;\n  color: #fff;\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple:focus,\n.select2-primary .select2-container--default .select2-selection--multiple:focus {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice,\n.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #007bff;\n  border-color: #006fe6;\n  color: #fff;\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice__remove,\n.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-primary.select2-container--focus .select2-selection--multiple,\n.select2-primary .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #80bdff;\n}\n\n.select2-secondary + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-secondary + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-secondary.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-secondary .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-secondary .select2-search--inline .select2-search__field:focus,\n.select2-secondary .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-secondary .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-secondary .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #afb5ba;\n}\n\n.select2-container--default .select2-secondary .select2-results__option--highlighted,\n.select2-secondary .select2-container--default .select2-results__option--highlighted {\n  background-color: #6c757d;\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-secondary .select2-results__option--highlighted[aria-selected]:hover,\n.select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #656d75;\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple:focus,\n.select2-secondary .select2-container--default .select2-selection--multiple:focus {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice,\n.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6c757d;\n  border-color: #60686f;\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice__remove,\n.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-secondary.select2-container--focus .select2-selection--multiple,\n.select2-secondary .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #afb5ba;\n}\n\n.select2-success + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-success + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-success.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-success .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-success .select2-search--inline .select2-search__field:focus,\n.select2-success .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-success .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-success .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #71dd8a;\n}\n\n.select2-container--default .select2-success .select2-results__option--highlighted,\n.select2-success .select2-container--default .select2-results__option--highlighted {\n  background-color: #28a745;\n  color: #fff;\n}\n\n.select2-container--default .select2-success .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-success .select2-results__option--highlighted[aria-selected]:hover,\n.select2-success .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-success .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #259b40;\n  color: #fff;\n}\n\n.select2-container--default .select2-success .select2-selection--multiple:focus,\n.select2-success .select2-container--default .select2-selection--multiple:focus {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice,\n.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #28a745;\n  border-color: #23923d;\n  color: #fff;\n}\n\n.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice__remove,\n.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-success.select2-container--focus .select2-selection--multiple,\n.select2-success .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #71dd8a;\n}\n\n.select2-info + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-info + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-info.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-info .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-info .select2-search--inline .select2-search__field:focus,\n.select2-info .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-info .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-info .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #63d9ec;\n}\n\n.select2-container--default .select2-info .select2-results__option--highlighted,\n.select2-info .select2-container--default .select2-results__option--highlighted {\n  background-color: #17a2b8;\n  color: #fff;\n}\n\n.select2-container--default .select2-info .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-info .select2-results__option--highlighted[aria-selected]:hover,\n.select2-info .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-info .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #1596aa;\n  color: #fff;\n}\n\n.select2-container--default .select2-info .select2-selection--multiple:focus,\n.select2-info .select2-container--default .select2-selection--multiple:focus {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice,\n.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #17a2b8;\n  border-color: #148ea1;\n  color: #fff;\n}\n\n.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice__remove,\n.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-info.select2-container--focus .select2-selection--multiple,\n.select2-info .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #63d9ec;\n}\n\n.select2-warning + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-warning + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-warning.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-warning .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-warning .select2-search--inline .select2-search__field:focus,\n.select2-warning .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-warning .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-warning .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #ffe187;\n}\n\n.select2-container--default .select2-warning .select2-results__option--highlighted,\n.select2-warning .select2-container--default .select2-results__option--highlighted {\n  background-color: #ffc107;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-warning .select2-results__option--highlighted[aria-selected]:hover,\n.select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f7b900;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple:focus,\n.select2-warning .select2-container--default .select2-selection--multiple:focus {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice,\n.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #ffc107;\n  border-color: #edb100;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice__remove,\n.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-warning.select2-container--focus .select2-selection--multiple,\n.select2-warning .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #ffe187;\n}\n\n.select2-danger + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-danger + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-danger.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-danger .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-danger .select2-search--inline .select2-search__field:focus,\n.select2-danger .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-danger .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-danger .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #efa2a9;\n}\n\n.select2-container--default .select2-danger .select2-results__option--highlighted,\n.select2-danger .select2-container--default .select2-results__option--highlighted {\n  background-color: #dc3545;\n  color: #fff;\n}\n\n.select2-container--default .select2-danger .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-danger .select2-results__option--highlighted[aria-selected]:hover,\n.select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #da2839;\n  color: #fff;\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple:focus,\n.select2-danger .select2-container--default .select2-selection--multiple:focus {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice,\n.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #dc3545;\n  border-color: #d32535;\n  color: #fff;\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice__remove,\n.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-danger.select2-container--focus .select2-selection--multiple,\n.select2-danger .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #efa2a9;\n}\n\n.select2-light + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: white;\n}\n\n.select2-light + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: white;\n}\n\n.select2-container--default .select2-light.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-light .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-light .select2-search--inline .select2-search__field:focus,\n.select2-light .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-light .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-light .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid white;\n}\n\n.select2-container--default .select2-light .select2-results__option--highlighted,\n.select2-light .select2-container--default .select2-results__option--highlighted {\n  background-color: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-light .select2-results__option--highlighted[aria-selected]:hover,\n.select2-light .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-light .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #eff1f4;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light .select2-selection--multiple:focus,\n.select2-light .select2-container--default .select2-selection--multiple:focus {\n  border-color: white;\n}\n\n.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice,\n.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f8f9fa;\n  border-color: #e9ecef;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice__remove,\n.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-light.select2-container--focus .select2-selection--multiple,\n.select2-light .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: white;\n}\n\n.select2-dark + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-dark + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-dark.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-dark .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-dark .select2-search--inline .select2-search__field:focus,\n.select2-dark .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-dark .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-dark .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #6d7a86;\n}\n\n.select2-container--default .select2-dark .select2-results__option--highlighted,\n.select2-dark .select2-container--default .select2-results__option--highlighted {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.select2-container--default .select2-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-dark .select2-results__option--highlighted[aria-selected]:hover,\n.select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2d3238;\n  color: #fff;\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple:focus,\n.select2-dark .select2-container--default .select2-selection--multiple:focus {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice,\n.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #343a40;\n  border-color: #292d32;\n  color: #fff;\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice__remove,\n.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-dark.select2-container--focus .select2-selection--multiple,\n.select2-dark .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #6d7a86;\n}\n\n.select2-lightblue + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #99c5de;\n}\n\n.select2-lightblue + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #99c5de;\n}\n\n.select2-container--default .select2-lightblue.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lightblue .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lightblue .select2-search--inline .select2-search__field:focus,\n.select2-lightblue .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-lightblue .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-lightblue .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #99c5de;\n}\n\n.select2-container--default .select2-lightblue .select2-results__option--highlighted,\n.select2-lightblue .select2-container--default .select2-results__option--highlighted {\n  background-color: #3c8dbc;\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-lightblue .select2-results__option--highlighted[aria-selected]:hover,\n.select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #3884b0;\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple:focus,\n.select2-lightblue .select2-container--default .select2-selection--multiple:focus {\n  border-color: #99c5de;\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice,\n.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3c8dbc;\n  border-color: #367fa9;\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove,\n.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-lightblue.select2-container--focus .select2-selection--multiple,\n.select2-lightblue .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #99c5de;\n}\n\n.select2-navy + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #005ebf;\n}\n\n.select2-navy + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #005ebf;\n}\n\n.select2-container--default .select2-navy.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-navy .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-navy .select2-search--inline .select2-search__field:focus,\n.select2-navy .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-navy .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-navy .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #005ebf;\n}\n\n.select2-container--default .select2-navy .select2-results__option--highlighted,\n.select2-navy .select2-container--default .select2-results__option--highlighted {\n  background-color: #001f3f;\n  color: #fff;\n}\n\n.select2-container--default .select2-navy .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-navy .select2-results__option--highlighted[aria-selected]:hover,\n.select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #001730;\n  color: #fff;\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple:focus,\n.select2-navy .select2-container--default .select2-selection--multiple:focus {\n  border-color: #005ebf;\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice,\n.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #001f3f;\n  border-color: #001226;\n  color: #fff;\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice__remove,\n.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-navy.select2-container--focus .select2-selection--multiple,\n.select2-navy .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #005ebf;\n}\n\n.select2-olive + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #87cfaf;\n}\n\n.select2-olive + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #87cfaf;\n}\n\n.select2-container--default .select2-olive.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-olive .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-olive .select2-search--inline .select2-search__field:focus,\n.select2-olive .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-olive .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-olive .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #87cfaf;\n}\n\n.select2-container--default .select2-olive .select2-results__option--highlighted,\n.select2-olive .select2-container--default .select2-results__option--highlighted {\n  background-color: #3d9970;\n  color: #fff;\n}\n\n.select2-container--default .select2-olive .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-olive .select2-results__option--highlighted[aria-selected]:hover,\n.select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #398e68;\n  color: #fff;\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple:focus,\n.select2-olive .select2-container--default .select2-selection--multiple:focus {\n  border-color: #87cfaf;\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice,\n.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3d9970;\n  border-color: #368763;\n  color: #fff;\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice__remove,\n.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-olive.select2-container--focus .select2-selection--multiple,\n.select2-olive .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #87cfaf;\n}\n\n.select2-lime + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #81ffb8;\n}\n\n.select2-lime + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #81ffb8;\n}\n\n.select2-container--default .select2-lime.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lime .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-lime .select2-search--inline .select2-search__field:focus,\n.select2-lime .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-lime .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-lime .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #81ffb8;\n}\n\n.select2-container--default .select2-lime .select2-results__option--highlighted,\n.select2-lime .select2-container--default .select2-results__option--highlighted {\n  background-color: #01ff70;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-lime .select2-results__option--highlighted[aria-selected]:hover,\n.select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #00f169;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple:focus,\n.select2-lime .select2-container--default .select2-selection--multiple:focus {\n  border-color: #81ffb8;\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice,\n.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #01ff70;\n  border-color: #00e765;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice__remove,\n.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-lime.select2-container--focus .select2-selection--multiple,\n.select2-lime .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #81ffb8;\n}\n\n.select2-fuchsia + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f88adf;\n}\n\n.select2-fuchsia + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f88adf;\n}\n\n.select2-container--default .select2-fuchsia.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-fuchsia .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-fuchsia .select2-search--inline .select2-search__field:focus,\n.select2-fuchsia .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-fuchsia .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-fuchsia .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f88adf;\n}\n\n.select2-container--default .select2-fuchsia .select2-results__option--highlighted,\n.select2-fuchsia .select2-container--default .select2-results__option--highlighted {\n  background-color: #f012be;\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-fuchsia .select2-results__option--highlighted[aria-selected]:hover,\n.select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e40eb4;\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple:focus,\n.select2-fuchsia .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f88adf;\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice,\n.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f012be;\n  border-color: #db0ead;\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove,\n.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-fuchsia.select2-container--focus .select2-selection--multiple,\n.select2-fuchsia .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f88adf;\n}\n\n.select2-maroon + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f083ab;\n}\n\n.select2-maroon + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f083ab;\n}\n\n.select2-container--default .select2-maroon.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-maroon .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-maroon .select2-search--inline .select2-search__field:focus,\n.select2-maroon .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-maroon .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-maroon .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f083ab;\n}\n\n.select2-container--default .select2-maroon .select2-results__option--highlighted,\n.select2-maroon .select2-container--default .select2-results__option--highlighted {\n  background-color: #d81b60;\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-maroon .select2-results__option--highlighted[aria-selected]:hover,\n.select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #ca195a;\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple:focus,\n.select2-maroon .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f083ab;\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice,\n.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #d81b60;\n  border-color: #c11856;\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice__remove,\n.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-maroon.select2-container--focus .select2-selection--multiple,\n.select2-maroon .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f083ab;\n}\n\n.select2-blue + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-blue + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-blue.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-blue .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-blue .select2-search--inline .select2-search__field:focus,\n.select2-blue .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-blue .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-blue .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #80bdff;\n}\n\n.select2-container--default .select2-blue .select2-results__option--highlighted,\n.select2-blue .select2-container--default .select2-results__option--highlighted {\n  background-color: #007bff;\n  color: #fff;\n}\n\n.select2-container--default .select2-blue .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-blue .select2-results__option--highlighted[aria-selected]:hover,\n.select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #0074f0;\n  color: #fff;\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple:focus,\n.select2-blue .select2-container--default .select2-selection--multiple:focus {\n  border-color: #80bdff;\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice,\n.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #007bff;\n  border-color: #006fe6;\n  color: #fff;\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice__remove,\n.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-blue.select2-container--focus .select2-selection--multiple,\n.select2-blue .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #80bdff;\n}\n\n.select2-indigo + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #b389f9;\n}\n\n.select2-indigo + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #b389f9;\n}\n\n.select2-container--default .select2-indigo.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-indigo .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-indigo .select2-search--inline .select2-search__field:focus,\n.select2-indigo .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-indigo .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-indigo .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #b389f9;\n}\n\n.select2-container--default .select2-indigo .select2-results__option--highlighted,\n.select2-indigo .select2-container--default .select2-results__option--highlighted {\n  background-color: #6610f2;\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-indigo .select2-results__option--highlighted[aria-selected]:hover,\n.select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #5f0de6;\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple:focus,\n.select2-indigo .select2-container--default .select2-selection--multiple:focus {\n  border-color: #b389f9;\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice,\n.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6610f2;\n  border-color: #5b0cdd;\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice__remove,\n.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-indigo.select2-container--focus .select2-selection--multiple,\n.select2-indigo .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #b389f9;\n}\n\n.select2-purple + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #b8a2e0;\n}\n\n.select2-purple + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #b8a2e0;\n}\n\n.select2-container--default .select2-purple.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-purple .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-purple .select2-search--inline .select2-search__field:focus,\n.select2-purple .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-purple .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-purple .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #b8a2e0;\n}\n\n.select2-container--default .select2-purple .select2-results__option--highlighted,\n.select2-purple .select2-container--default .select2-results__option--highlighted {\n  background-color: #6f42c1;\n  color: #fff;\n}\n\n.select2-container--default .select2-purple .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-purple .select2-results__option--highlighted[aria-selected]:hover,\n.select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #683cb8;\n  color: #fff;\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple:focus,\n.select2-purple .select2-container--default .select2-selection--multiple:focus {\n  border-color: #b8a2e0;\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice,\n.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6f42c1;\n  border-color: #643ab0;\n  color: #fff;\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice__remove,\n.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-purple.select2-container--focus .select2-selection--multiple,\n.select2-purple .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #b8a2e0;\n}\n\n.select2-pink + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f6b0d0;\n}\n\n.select2-pink + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f6b0d0;\n}\n\n.select2-container--default .select2-pink.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-pink .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-pink .select2-search--inline .select2-search__field:focus,\n.select2-pink .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-pink .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-pink .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f6b0d0;\n}\n\n.select2-container--default .select2-pink .select2-results__option--highlighted,\n.select2-pink .select2-container--default .select2-results__option--highlighted {\n  background-color: #e83e8c;\n  color: #fff;\n}\n\n.select2-container--default .select2-pink .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-pink .select2-results__option--highlighted[aria-selected]:hover,\n.select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e63084;\n  color: #fff;\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple:focus,\n.select2-pink .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f6b0d0;\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice,\n.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #e83e8c;\n  border-color: #e5277e;\n  color: #fff;\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice__remove,\n.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-pink.select2-container--focus .select2-selection--multiple,\n.select2-pink .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f6b0d0;\n}\n\n.select2-red + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-red + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-red.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-red .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-red .select2-search--inline .select2-search__field:focus,\n.select2-red .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-red .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-red .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #efa2a9;\n}\n\n.select2-container--default .select2-red .select2-results__option--highlighted,\n.select2-red .select2-container--default .select2-results__option--highlighted {\n  background-color: #dc3545;\n  color: #fff;\n}\n\n.select2-container--default .select2-red .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-red .select2-results__option--highlighted[aria-selected]:hover,\n.select2-red .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-red .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #da2839;\n  color: #fff;\n}\n\n.select2-container--default .select2-red .select2-selection--multiple:focus,\n.select2-red .select2-container--default .select2-selection--multiple:focus {\n  border-color: #efa2a9;\n}\n\n.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice,\n.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #dc3545;\n  border-color: #d32535;\n  color: #fff;\n}\n\n.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice__remove,\n.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-red.select2-container--focus .select2-selection--multiple,\n.select2-red .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #efa2a9;\n}\n\n.select2-orange + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #fec392;\n}\n\n.select2-orange + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #fec392;\n}\n\n.select2-container--default .select2-orange.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-orange .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-orange .select2-search--inline .select2-search__field:focus,\n.select2-orange .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-orange .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-orange .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #fec392;\n}\n\n.select2-container--default .select2-orange .select2-results__option--highlighted,\n.select2-orange .select2-container--default .select2-results__option--highlighted {\n  background-color: #fd7e14;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-orange .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-orange .select2-results__option--highlighted[aria-selected]:hover,\n.select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #fd7605;\n  color: #fff;\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple:focus,\n.select2-orange .select2-container--default .select2-selection--multiple:focus {\n  border-color: #fec392;\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice,\n.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #fd7e14;\n  border-color: #f57102;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice__remove,\n.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-orange.select2-container--focus .select2-selection--multiple,\n.select2-orange .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #fec392;\n}\n\n.select2-yellow + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-yellow + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-yellow.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-yellow .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-yellow .select2-search--inline .select2-search__field:focus,\n.select2-yellow .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-yellow .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-yellow .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #ffe187;\n}\n\n.select2-container--default .select2-yellow .select2-results__option--highlighted,\n.select2-yellow .select2-container--default .select2-results__option--highlighted {\n  background-color: #ffc107;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-yellow .select2-results__option--highlighted[aria-selected]:hover,\n.select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f7b900;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple:focus,\n.select2-yellow .select2-container--default .select2-selection--multiple:focus {\n  border-color: #ffe187;\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice,\n.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #ffc107;\n  border-color: #edb100;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice__remove,\n.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-yellow.select2-container--focus .select2-selection--multiple,\n.select2-yellow .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #ffe187;\n}\n\n.select2-green + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-green + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-green.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-green .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-green .select2-search--inline .select2-search__field:focus,\n.select2-green .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-green .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-green .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #71dd8a;\n}\n\n.select2-container--default .select2-green .select2-results__option--highlighted,\n.select2-green .select2-container--default .select2-results__option--highlighted {\n  background-color: #28a745;\n  color: #fff;\n}\n\n.select2-container--default .select2-green .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-green .select2-results__option--highlighted[aria-selected]:hover,\n.select2-green .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-green .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #259b40;\n  color: #fff;\n}\n\n.select2-container--default .select2-green .select2-selection--multiple:focus,\n.select2-green .select2-container--default .select2-selection--multiple:focus {\n  border-color: #71dd8a;\n}\n\n.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice,\n.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #28a745;\n  border-color: #23923d;\n  color: #fff;\n}\n\n.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice__remove,\n.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-green.select2-container--focus .select2-selection--multiple,\n.select2-green .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #71dd8a;\n}\n\n.select2-teal + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #7eeaca;\n}\n\n.select2-teal + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #7eeaca;\n}\n\n.select2-container--default .select2-teal.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-teal .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-teal .select2-search--inline .select2-search__field:focus,\n.select2-teal .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-teal .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-teal .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #7eeaca;\n}\n\n.select2-container--default .select2-teal .select2-results__option--highlighted,\n.select2-teal .select2-container--default .select2-results__option--highlighted {\n  background-color: #20c997;\n  color: #fff;\n}\n\n.select2-container--default .select2-teal .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-teal .select2-results__option--highlighted[aria-selected]:hover,\n.select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #1ebc8d;\n  color: #fff;\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple:focus,\n.select2-teal .select2-container--default .select2-selection--multiple:focus {\n  border-color: #7eeaca;\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice,\n.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #20c997;\n  border-color: #1cb386;\n  color: #fff;\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice__remove,\n.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-teal.select2-container--focus .select2-selection--multiple,\n.select2-teal .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #7eeaca;\n}\n\n.select2-cyan + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-cyan + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-cyan.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-cyan .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-cyan .select2-search--inline .select2-search__field:focus,\n.select2-cyan .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-cyan .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-cyan .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #63d9ec;\n}\n\n.select2-container--default .select2-cyan .select2-results__option--highlighted,\n.select2-cyan .select2-container--default .select2-results__option--highlighted {\n  background-color: #17a2b8;\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-cyan .select2-results__option--highlighted[aria-selected]:hover,\n.select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #1596aa;\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple:focus,\n.select2-cyan .select2-container--default .select2-selection--multiple:focus {\n  border-color: #63d9ec;\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice,\n.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #17a2b8;\n  border-color: #148ea1;\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice__remove,\n.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-cyan.select2-container--focus .select2-selection--multiple,\n.select2-cyan .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #63d9ec;\n}\n\n.select2-white + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: white;\n}\n\n.select2-white + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: white;\n}\n\n.select2-container--default .select2-white.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-white .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-white .select2-search--inline .select2-search__field:focus,\n.select2-white .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-white .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-white .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid white;\n}\n\n.select2-container--default .select2-white .select2-results__option--highlighted,\n.select2-white .select2-container--default .select2-results__option--highlighted {\n  background-color: #fff;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-white .select2-results__option--highlighted[aria-selected]:hover,\n.select2-white .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-white .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f7f7f7;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white .select2-selection--multiple:focus,\n.select2-white .select2-container--default .select2-selection--multiple:focus {\n  border-color: white;\n}\n\n.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice,\n.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #fff;\n  border-color: #f2f2f2;\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice__remove,\n.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .select2-white.select2-container--focus .select2-selection--multiple,\n.select2-white .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: white;\n}\n\n.select2-gray + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-gray + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-gray.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray .select2-search--inline .select2-search__field:focus,\n.select2-gray .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-gray .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-gray .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #afb5ba;\n}\n\n.select2-container--default .select2-gray .select2-results__option--highlighted,\n.select2-gray .select2-container--default .select2-results__option--highlighted {\n  background-color: #6c757d;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-gray .select2-results__option--highlighted[aria-selected]:hover,\n.select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #656d75;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple:focus,\n.select2-gray .select2-container--default .select2-selection--multiple:focus {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice,\n.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6c757d;\n  border-color: #60686f;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice__remove,\n.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-gray.select2-container--focus .select2-selection--multiple,\n.select2-gray .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #afb5ba;\n}\n\n.select2-gray-dark + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-gray-dark + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-gray-dark.select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray-dark .select2-dropdown .select2-search__field:focus,\n.select2-container--default .select2-gray-dark .select2-search--inline .select2-search__field:focus,\n.select2-gray-dark .select2-container--default.select2-dropdown .select2-search__field:focus,\n.select2-gray-dark .select2-container--default .select2-dropdown .select2-search__field:focus,\n.select2-gray-dark .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #6d7a86;\n}\n\n.select2-container--default .select2-gray-dark .select2-results__option--highlighted,\n.select2-gray-dark .select2-container--default .select2-results__option--highlighted {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-gray-dark .select2-results__option--highlighted[aria-selected]:hover,\n.select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected],\n.select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2d3238;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple:focus,\n.select2-gray-dark .select2-container--default .select2-selection--multiple:focus {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice,\n.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #343a40;\n  border-color: #292d32;\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove,\n.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove:hover,\n.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .select2-gray-dark.select2-container--focus .select2-selection--multiple,\n.select2-gray-dark .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #6d7a86;\n}\n\n.dark-mode .select2-selection {\n  background-color: #343a40;\n  border-color: #6c757d;\n}\n\n.dark-mode .select2-container--disabled .select2-selection--single {\n  background-color: #454d55;\n}\n\n.dark-mode .select2-selection--single {\n  background-color: #343a40;\n  border-color: #6c757d;\n}\n\n.dark-mode .select2-selection--single .select2-selection__rendered {\n  color: #fff;\n}\n\n.dark-mode .select2-dropdown .select2-search__field,\n.dark-mode .select2-search--inline .select2-search__field {\n  background-color: #343a40;\n  border-color: #6c757d;\n  color: white;\n}\n\n.dark-mode .select2-dropdown {\n  background-color: #343a40;\n  border-color: #6c757d;\n  color: white;\n}\n\n.dark-mode .select2-results__option[aria-selected=\"true\"] {\n  background-color: #3f474e !important;\n  color: #dee2e6;\n}\n\n.dark-mode .select2-container .select2-search--inline .select2-search__field {\n  background-color: transparent;\n  color: #fff;\n}\n\n.dark-mode .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {\n  color: #fff;\n}\n\n.dark-mode .select2-primary + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #85a7ca;\n}\n\n.dark-mode .select2-primary + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-primary.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-primary .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-primary .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-primary .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-primary .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-primary .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-results__option--highlighted,\n.dark-mode .select2-primary .select2-container--default .select2-results__option--highlighted {\n  background-color: #3f6791;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-primary .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #3a5f86;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-selection--multiple:focus,\n.dark-mode .select2-primary .select2-container--default .select2-selection--multiple:focus {\n  border-color: #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3f6791;\n  border-color: #375a7f;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-primary .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-primary.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-primary .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #85a7ca;\n}\n\n.dark-mode .select2-secondary + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.dark-mode .select2-secondary + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-secondary.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-secondary .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-secondary .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-secondary .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-secondary .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-secondary .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-results__option--highlighted,\n.dark-mode .select2-secondary .select2-container--default .select2-results__option--highlighted {\n  background-color: #6c757d;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-secondary .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #656d75;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple:focus,\n.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple:focus {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6c757d;\n  border-color: #60686f;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-secondary.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-secondary .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #afb5ba;\n}\n\n.dark-mode .select2-success + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #3dffcd;\n}\n\n.dark-mode .select2-success + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-success.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-success .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-success .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-success .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-success .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-success .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-success .select2-results__option--highlighted,\n.dark-mode .select2-success .select2-container--default .select2-results__option--highlighted {\n  background-color: #00bc8c;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-success .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-success .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-success .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-success .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #00ad81;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-success .select2-selection--multiple:focus,\n.dark-mode .select2-success .select2-container--default .select2-selection--multiple:focus {\n  border-color: #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-success .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #00bc8c;\n  border-color: #00a379;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-success .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-success .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-success.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-success .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #3dffcd;\n}\n\n.dark-mode .select2-info + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #a0cfee;\n}\n\n.dark-mode .select2-info + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-info.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-info .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-info .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-info .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-info .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-info .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-info .select2-results__option--highlighted,\n.dark-mode .select2-info .select2-container--default .select2-results__option--highlighted {\n  background-color: #3498db;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-info .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-info .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-info .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-info .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2791d9;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-info .select2-selection--multiple:focus,\n.dark-mode .select2-info .select2-container--default .select2-selection--multiple:focus {\n  border-color: #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-info .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3498db;\n  border-color: #258cd1;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-info .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-info .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-info.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-info .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #a0cfee;\n}\n\n.dark-mode .select2-warning + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f9cf8b;\n}\n\n.dark-mode .select2-warning + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-warning.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-warning .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-warning .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-warning .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-warning .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-warning .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-results__option--highlighted,\n.dark-mode .select2-warning .select2-container--default .select2-results__option--highlighted {\n  background-color: #f39c12;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-warning .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #ea940c;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-selection--multiple:focus,\n.dark-mode .select2-warning .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f39c12;\n  border-color: #e08e0b;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-warning .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-warning.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-warning .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f9cf8b;\n}\n\n.dark-mode .select2-danger + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f5b4ae;\n}\n\n.dark-mode .select2-danger + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-danger.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-danger .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-danger .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-danger .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-danger .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-danger .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-results__option--highlighted,\n.dark-mode .select2-danger .select2-container--default .select2-results__option--highlighted {\n  background-color: #e74c3c;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-danger .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e53f2e;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-selection--multiple:focus,\n.dark-mode .select2-danger .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #e74c3c;\n  border-color: #e43725;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-danger .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-danger.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-danger .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f5b4ae;\n}\n\n.dark-mode .select2-light + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: white;\n}\n\n.dark-mode .select2-light + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: white;\n}\n\n.select2-container--default .dark-mode .select2-light.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-light .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-light .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-light .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-light .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-light .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid white;\n}\n\n.select2-container--default .dark-mode .select2-light .select2-results__option--highlighted,\n.dark-mode .select2-light .select2-container--default .select2-results__option--highlighted {\n  background-color: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-light .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-light .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-light .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-light .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #eff1f4;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-light .select2-selection--multiple:focus,\n.dark-mode .select2-light .select2-container--default .select2-selection--multiple:focus {\n  border-color: white;\n}\n\n.select2-container--default .dark-mode .select2-light .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f8f9fa;\n  border-color: #e9ecef;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-light .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-light .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-light.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-light .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: white;\n}\n\n.dark-mode .select2-dark + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.dark-mode .select2-dark + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-dark.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-dark .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-dark .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-dark .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-dark .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-dark .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-results__option--highlighted,\n.dark-mode .select2-dark .select2-container--default .select2-results__option--highlighted {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-dark .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2d3238;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-selection--multiple:focus,\n.dark-mode .select2-dark .select2-container--default .select2-selection--multiple:focus {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #343a40;\n  border-color: #292d32;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-dark .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-dark.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-dark .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #6d7a86;\n}\n\n.dark-mode .select2-lightblue + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #e6f1f7;\n}\n\n.dark-mode .select2-lightblue + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #e6f1f7;\n}\n\n.select2-container--default .dark-mode .select2-lightblue.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-lightblue .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-lightblue .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-lightblue .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-lightblue .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-lightblue .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #e6f1f7;\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-results__option--highlighted,\n.dark-mode .select2-lightblue .select2-container--default .select2-results__option--highlighted {\n  background-color: #86bad8;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-lightblue .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #7ab3d5;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple:focus,\n.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple:focus {\n  border-color: #e6f1f7;\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #86bad8;\n  border-color: #72afd2;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lightblue.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-lightblue .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #e6f1f7;\n}\n\n.dark-mode .select2-navy + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #006ad8;\n}\n\n.dark-mode .select2-navy + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #006ad8;\n}\n\n.select2-container--default .dark-mode .select2-navy.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-navy .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-navy .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-navy .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-navy .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-navy .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #006ad8;\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-results__option--highlighted,\n.dark-mode .select2-navy .select2-container--default .select2-results__option--highlighted {\n  background-color: #002c59;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-navy .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #002449;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-selection--multiple:focus,\n.dark-mode .select2-navy .select2-container--default .select2-selection--multiple:focus {\n  border-color: #006ad8;\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #002c59;\n  border-color: #001f3f;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-navy .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-navy.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-navy .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #006ad8;\n}\n\n.dark-mode .select2-olive + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #cfecdf;\n}\n\n.dark-mode .select2-olive + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #cfecdf;\n}\n\n.select2-container--default .dark-mode .select2-olive.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-olive .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-olive .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-olive .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-olive .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-olive .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #cfecdf;\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-results__option--highlighted,\n.dark-mode .select2-olive .select2-container--default .select2-results__option--highlighted {\n  background-color: #74c8a3;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-olive .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #69c39b;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-selection--multiple:focus,\n.dark-mode .select2-olive .select2-container--default .select2-selection--multiple:focus {\n  border-color: #cfecdf;\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #74c8a3;\n  border-color: #62c096;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-olive .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-olive.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-olive .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #cfecdf;\n}\n\n.dark-mode .select2-lime + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #e7fff1;\n}\n\n.dark-mode .select2-lime + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #e7fff1;\n}\n\n.select2-container--default .dark-mode .select2-lime.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-lime .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-lime .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-lime .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-lime .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-lime .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #e7fff1;\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-results__option--highlighted,\n.dark-mode .select2-lime .select2-container--default .select2-results__option--highlighted {\n  background-color: #67ffa9;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-lime .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #58ffa1;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-selection--multiple:focus,\n.dark-mode .select2-lime .select2-container--default .select2-selection--multiple:focus {\n  border-color: #e7fff1;\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #67ffa9;\n  border-color: #4eff9b;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-lime .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-lime.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-lime .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #e7fff1;\n}\n\n.dark-mode .select2-fuchsia + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #feeaf9;\n}\n\n.dark-mode .select2-fuchsia + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #feeaf9;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-fuchsia .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-fuchsia .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-fuchsia .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-fuchsia .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-fuchsia .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #feeaf9;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-results__option--highlighted,\n.dark-mode .select2-fuchsia .select2-container--default .select2-results__option--highlighted {\n  background-color: #f672d8;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-fuchsia .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f564d4;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple:focus,\n.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple:focus {\n  border-color: #feeaf9;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f672d8;\n  border-color: #f55ad2;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-fuchsia.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-fuchsia .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #feeaf9;\n}\n\n.dark-mode .select2-maroon + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #fbdee8;\n}\n\n.dark-mode .select2-maroon + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #fbdee8;\n}\n\n.select2-container--default .dark-mode .select2-maroon.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-maroon .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-maroon .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-maroon .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-maroon .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-maroon .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #fbdee8;\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-results__option--highlighted,\n.dark-mode .select2-maroon .select2-container--default .select2-results__option--highlighted {\n  background-color: #ed6c9b;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-maroon .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #eb5f92;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple:focus,\n.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple:focus {\n  border-color: #fbdee8;\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #ed6c9b;\n  border-color: #ea568c;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-maroon.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-maroon .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #fbdee8;\n}\n\n.dark-mode .select2-blue + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #85a7ca;\n}\n\n.dark-mode .select2-blue + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-blue.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-blue .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-blue .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-blue .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-blue .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-blue .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-results__option--highlighted,\n.dark-mode .select2-blue .select2-container--default .select2-results__option--highlighted {\n  background-color: #3f6791;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-blue .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #3a5f86;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-selection--multiple:focus,\n.dark-mode .select2-blue .select2-container--default .select2-selection--multiple:focus {\n  border-color: #85a7ca;\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3f6791;\n  border-color: #375a7f;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-blue .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-blue.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-blue .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #85a7ca;\n}\n\n.dark-mode .select2-indigo + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #b389f9;\n}\n\n.dark-mode .select2-indigo + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #b389f9;\n}\n\n.select2-container--default .dark-mode .select2-indigo.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-indigo .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-indigo .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-indigo .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-indigo .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-indigo .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #b389f9;\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-results__option--highlighted,\n.dark-mode .select2-indigo .select2-container--default .select2-results__option--highlighted {\n  background-color: #6610f2;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-indigo .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #5f0de6;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple:focus,\n.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple:focus {\n  border-color: #b389f9;\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6610f2;\n  border-color: #5b0cdd;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-indigo.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-indigo .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #b389f9;\n}\n\n.dark-mode .select2-purple + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #b8a2e0;\n}\n\n.dark-mode .select2-purple + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #b8a2e0;\n}\n\n.select2-container--default .dark-mode .select2-purple.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-purple .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-purple .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-purple .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-purple .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-purple .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #b8a2e0;\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-results__option--highlighted,\n.dark-mode .select2-purple .select2-container--default .select2-results__option--highlighted {\n  background-color: #6f42c1;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-purple .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #683cb8;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-selection--multiple:focus,\n.dark-mode .select2-purple .select2-container--default .select2-selection--multiple:focus {\n  border-color: #b8a2e0;\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6f42c1;\n  border-color: #643ab0;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-purple .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-purple.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-purple .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #b8a2e0;\n}\n\n.dark-mode .select2-pink + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f6b0d0;\n}\n\n.dark-mode .select2-pink + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f6b0d0;\n}\n\n.select2-container--default .dark-mode .select2-pink.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-pink .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-pink .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-pink .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-pink .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-pink .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f6b0d0;\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-results__option--highlighted,\n.dark-mode .select2-pink .select2-container--default .select2-results__option--highlighted {\n  background-color: #e83e8c;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-pink .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e63084;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-selection--multiple:focus,\n.dark-mode .select2-pink .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f6b0d0;\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #e83e8c;\n  border-color: #e5277e;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-pink .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-pink.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-pink .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f6b0d0;\n}\n\n.dark-mode .select2-red + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f5b4ae;\n}\n\n.dark-mode .select2-red + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-red.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-red .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-red .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-red .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-red .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-red .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-red .select2-results__option--highlighted,\n.dark-mode .select2-red .select2-container--default .select2-results__option--highlighted {\n  background-color: #e74c3c;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-red .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-red .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-red .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-red .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #e53f2e;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-red .select2-selection--multiple:focus,\n.dark-mode .select2-red .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f5b4ae;\n}\n\n.select2-container--default .dark-mode .select2-red .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #e74c3c;\n  border-color: #e43725;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-red .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-red .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-red.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-red .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f5b4ae;\n}\n\n.dark-mode .select2-orange + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #fec392;\n}\n\n.dark-mode .select2-orange + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #fec392;\n}\n\n.select2-container--default .dark-mode .select2-orange.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-orange .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-orange .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-orange .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-orange .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-orange .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #fec392;\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-results__option--highlighted,\n.dark-mode .select2-orange .select2-container--default .select2-results__option--highlighted {\n  background-color: #fd7e14;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-orange .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #fd7605;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-selection--multiple:focus,\n.dark-mode .select2-orange .select2-container--default .select2-selection--multiple:focus {\n  border-color: #fec392;\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #fd7e14;\n  border-color: #f57102;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-orange .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-orange.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-orange .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #fec392;\n}\n\n.dark-mode .select2-yellow + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #f9cf8b;\n}\n\n.dark-mode .select2-yellow + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-yellow.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-yellow .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-yellow .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-yellow .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-yellow .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-yellow .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-results__option--highlighted,\n.dark-mode .select2-yellow .select2-container--default .select2-results__option--highlighted {\n  background-color: #f39c12;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-yellow .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #ea940c;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple:focus,\n.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple:focus {\n  border-color: #f9cf8b;\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #f39c12;\n  border-color: #e08e0b;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-yellow.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-yellow .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #f9cf8b;\n}\n\n.dark-mode .select2-green + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #3dffcd;\n}\n\n.dark-mode .select2-green + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-green.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-green .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-green .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-green .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-green .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-green .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-green .select2-results__option--highlighted,\n.dark-mode .select2-green .select2-container--default .select2-results__option--highlighted {\n  background-color: #00bc8c;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-green .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-green .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-green .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-green .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #00ad81;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-green .select2-selection--multiple:focus,\n.dark-mode .select2-green .select2-container--default .select2-selection--multiple:focus {\n  border-color: #3dffcd;\n}\n\n.select2-container--default .dark-mode .select2-green .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #00bc8c;\n  border-color: #00a379;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-green .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-green .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-green.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-green .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #3dffcd;\n}\n\n.dark-mode .select2-teal + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #7eeaca;\n}\n\n.dark-mode .select2-teal + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #7eeaca;\n}\n\n.select2-container--default .dark-mode .select2-teal.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-teal .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-teal .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-teal .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-teal .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-teal .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #7eeaca;\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-results__option--highlighted,\n.dark-mode .select2-teal .select2-container--default .select2-results__option--highlighted {\n  background-color: #20c997;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-teal .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #1ebc8d;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-selection--multiple:focus,\n.dark-mode .select2-teal .select2-container--default .select2-selection--multiple:focus {\n  border-color: #7eeaca;\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #20c997;\n  border-color: #1cb386;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-teal .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-teal.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-teal .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #7eeaca;\n}\n\n.dark-mode .select2-cyan + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #a0cfee;\n}\n\n.dark-mode .select2-cyan + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-cyan.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-cyan .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-cyan .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-cyan .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-cyan .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-cyan .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-results__option--highlighted,\n.dark-mode .select2-cyan .select2-container--default .select2-results__option--highlighted {\n  background-color: #3498db;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-cyan .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2791d9;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple:focus,\n.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple:focus {\n  border-color: #a0cfee;\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #3498db;\n  border-color: #258cd1;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-cyan.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-cyan .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #a0cfee;\n}\n\n.dark-mode .select2-white + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: white;\n}\n\n.dark-mode .select2-white + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: white;\n}\n\n.select2-container--default .dark-mode .select2-white.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-white .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-white .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-white .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-white .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-white .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid white;\n}\n\n.select2-container--default .dark-mode .select2-white .select2-results__option--highlighted,\n.dark-mode .select2-white .select2-container--default .select2-results__option--highlighted {\n  background-color: #fff;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-white .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-white .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-white .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-white .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #f7f7f7;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-white .select2-selection--multiple:focus,\n.dark-mode .select2-white .select2-container--default .select2-selection--multiple:focus {\n  border-color: white;\n}\n\n.select2-container--default .dark-mode .select2-white .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #fff;\n  border-color: #f2f2f2;\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-white .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(31, 45, 61, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-white .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #1f2d3d;\n}\n\n.select2-container--default .dark-mode .select2-white.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-white .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: white;\n}\n\n.dark-mode .select2-gray + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.dark-mode .select2-gray + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-gray.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-gray .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-gray .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-gray .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-gray .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-gray .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-results__option--highlighted,\n.dark-mode .select2-gray .select2-container--default .select2-results__option--highlighted {\n  background-color: #6c757d;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-gray .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #656d75;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-selection--multiple:focus,\n.dark-mode .select2-gray .select2-container--default .select2-selection--multiple:focus {\n  border-color: #afb5ba;\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #6c757d;\n  border-color: #60686f;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-gray .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-gray .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #afb5ba;\n}\n\n.dark-mode .select2-gray-dark + .select2-container--default.select2-container--open .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.dark-mode .select2-gray-dark + .select2-container--default.select2-container--focus .select2-selection--single {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark.select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-gray-dark .select2-dropdown .select2-search__field:focus,\n.select2-container--default .dark-mode .select2-gray-dark .select2-search--inline .select2-search__field:focus,\n.dark-mode .select2-gray-dark .select2-container--default.select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-gray-dark .select2-container--default .select2-dropdown .select2-search__field:focus,\n.dark-mode .select2-gray-dark .select2-container--default .select2-search--inline .select2-search__field:focus {\n  border: 1px solid #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-results__option--highlighted,\n.dark-mode .select2-gray-dark .select2-container--default .select2-results__option--highlighted {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-gray-dark .select2-results__option--highlighted[aria-selected]:hover,\n.dark-mode .select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected],\n.dark-mode .select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {\n  background-color: #2d3238;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple:focus,\n.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple:focus {\n  border-color: #6d7a86;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple .select2-selection__choice,\n.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {\n  background-color: #343a40;\n  border-color: #292d32;\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove,\n.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove:hover,\n.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {\n  color: #fff;\n}\n\n.select2-container--default .dark-mode .select2-gray-dark.select2-container--focus .select2-selection--multiple,\n.dark-mode .select2-gray-dark .select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #6d7a86;\n}\n\n.slider .tooltip.in {\n  opacity: 0.9;\n}\n\n.slider.slider-vertical {\n  height: 100%;\n}\n\n.slider.slider-horizontal {\n  width: 100%;\n}\n\n.slider-primary .slider .slider-selection {\n  background: #007bff;\n}\n\n.slider-secondary .slider .slider-selection {\n  background: #6c757d;\n}\n\n.slider-success .slider .slider-selection {\n  background: #28a745;\n}\n\n.slider-info .slider .slider-selection {\n  background: #17a2b8;\n}\n\n.slider-warning .slider .slider-selection {\n  background: #ffc107;\n}\n\n.slider-danger .slider .slider-selection {\n  background: #dc3545;\n}\n\n.slider-light .slider .slider-selection {\n  background: #f8f9fa;\n}\n\n.slider-dark .slider .slider-selection {\n  background: #343a40;\n}\n\n.slider-lightblue .slider .slider-selection {\n  background: #3c8dbc;\n}\n\n.slider-navy .slider .slider-selection {\n  background: #001f3f;\n}\n\n.slider-olive .slider .slider-selection {\n  background: #3d9970;\n}\n\n.slider-lime .slider .slider-selection {\n  background: #01ff70;\n}\n\n.slider-fuchsia .slider .slider-selection {\n  background: #f012be;\n}\n\n.slider-maroon .slider .slider-selection {\n  background: #d81b60;\n}\n\n.slider-blue .slider .slider-selection {\n  background: #007bff;\n}\n\n.slider-indigo .slider .slider-selection {\n  background: #6610f2;\n}\n\n.slider-purple .slider .slider-selection {\n  background: #6f42c1;\n}\n\n.slider-pink .slider .slider-selection {\n  background: #e83e8c;\n}\n\n.slider-red .slider .slider-selection {\n  background: #dc3545;\n}\n\n.slider-orange .slider .slider-selection {\n  background: #fd7e14;\n}\n\n.slider-yellow .slider .slider-selection {\n  background: #ffc107;\n}\n\n.slider-green .slider .slider-selection {\n  background: #28a745;\n}\n\n.slider-teal .slider .slider-selection {\n  background: #20c997;\n}\n\n.slider-cyan .slider .slider-selection {\n  background: #17a2b8;\n}\n\n.slider-white .slider .slider-selection {\n  background: #fff;\n}\n\n.slider-gray .slider .slider-selection {\n  background: #6c757d;\n}\n\n.slider-gray-dark .slider .slider-selection {\n  background: #343a40;\n}\n\n.dark-mode .slider-track {\n  background-color: #4b545c;\n  background-image: none;\n}\n\n.dark-mode .slider-primary .slider .slider-selection {\n  background: #3f6791;\n}\n\n.dark-mode .slider-secondary .slider .slider-selection {\n  background: #6c757d;\n}\n\n.dark-mode .slider-success .slider .slider-selection {\n  background: #00bc8c;\n}\n\n.dark-mode .slider-info .slider .slider-selection {\n  background: #3498db;\n}\n\n.dark-mode .slider-warning .slider .slider-selection {\n  background: #f39c12;\n}\n\n.dark-mode .slider-danger .slider .slider-selection {\n  background: #e74c3c;\n}\n\n.dark-mode .slider-light .slider .slider-selection {\n  background: #f8f9fa;\n}\n\n.dark-mode .slider-dark .slider .slider-selection {\n  background: #343a40;\n}\n\n.dark-mode .slider-lightblue .slider .slider-selection {\n  background: #86bad8;\n}\n\n.dark-mode .slider-navy .slider .slider-selection {\n  background: #002c59;\n}\n\n.dark-mode .slider-olive .slider .slider-selection {\n  background: #74c8a3;\n}\n\n.dark-mode .slider-lime .slider .slider-selection {\n  background: #67ffa9;\n}\n\n.dark-mode .slider-fuchsia .slider .slider-selection {\n  background: #f672d8;\n}\n\n.dark-mode .slider-maroon .slider .slider-selection {\n  background: #ed6c9b;\n}\n\n.dark-mode .slider-blue .slider .slider-selection {\n  background: #3f6791;\n}\n\n.dark-mode .slider-indigo .slider .slider-selection {\n  background: #6610f2;\n}\n\n.dark-mode .slider-purple .slider .slider-selection {\n  background: #6f42c1;\n}\n\n.dark-mode .slider-pink .slider .slider-selection {\n  background: #e83e8c;\n}\n\n.dark-mode .slider-red .slider .slider-selection {\n  background: #e74c3c;\n}\n\n.dark-mode .slider-orange .slider .slider-selection {\n  background: #fd7e14;\n}\n\n.dark-mode .slider-yellow .slider .slider-selection {\n  background: #f39c12;\n}\n\n.dark-mode .slider-green .slider .slider-selection {\n  background: #00bc8c;\n}\n\n.dark-mode .slider-teal .slider .slider-selection {\n  background: #20c997;\n}\n\n.dark-mode .slider-cyan .slider .slider-selection {\n  background: #3498db;\n}\n\n.dark-mode .slider-white .slider .slider-selection {\n  background: #fff;\n}\n\n.dark-mode .slider-gray .slider .slider-selection {\n  background: #6c757d;\n}\n\n.dark-mode .slider-gray-dark .slider .slider-selection {\n  background: #343a40;\n}\n\n.icheck-primary > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-primary > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #007bff;\n}\n\n.icheck-primary > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-primary > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #007bff;\n}\n\n.icheck-primary > input:first-child:checked + label::before,\n.icheck-primary > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-secondary > input:first-child:checked + label::before,\n.icheck-secondary > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.icheck-success > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-success > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-success > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-success > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-success > input:first-child:checked + label::before,\n.icheck-success > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #28a745;\n  border-color: #28a745;\n}\n\n.icheck-info > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-info > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-info > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-info > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-info > input:first-child:checked + label::before,\n.icheck-info > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n\n.icheck-warning > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-warning > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-warning > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-warning > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-warning > input:first-child:checked + label::before,\n.icheck-warning > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n\n.icheck-danger > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-danger > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-danger > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-danger > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-danger > input:first-child:checked + label::before,\n.icheck-danger > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.icheck-light > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-light > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f8f9fa;\n}\n\n.icheck-light > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-light > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f8f9fa;\n}\n\n.icheck-light > input:first-child:checked + label::before,\n.icheck-light > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n\n.icheck-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-dark > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-dark > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-dark > input:first-child:checked + label::before,\n.icheck-dark > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3c8dbc;\n}\n\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3c8dbc;\n}\n\n.icheck-lightblue > input:first-child:checked + label::before,\n.icheck-lightblue > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3c8dbc;\n  border-color: #3c8dbc;\n}\n\n.icheck-navy > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-navy > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #001f3f;\n}\n\n.icheck-navy > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-navy > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #001f3f;\n}\n\n.icheck-navy > input:first-child:checked + label::before,\n.icheck-navy > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #001f3f;\n  border-color: #001f3f;\n}\n\n.icheck-olive > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-olive > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3d9970;\n}\n\n.icheck-olive > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-olive > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3d9970;\n}\n\n.icheck-olive > input:first-child:checked + label::before,\n.icheck-olive > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3d9970;\n  border-color: #3d9970;\n}\n\n.icheck-lime > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-lime > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #01ff70;\n}\n\n.icheck-lime > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-lime > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #01ff70;\n}\n\n.icheck-lime > input:first-child:checked + label::before,\n.icheck-lime > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #01ff70;\n  border-color: #01ff70;\n}\n\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f012be;\n}\n\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f012be;\n}\n\n.icheck-fuchsia > input:first-child:checked + label::before,\n.icheck-fuchsia > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f012be;\n  border-color: #f012be;\n}\n\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #d81b60;\n}\n\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #d81b60;\n}\n\n.icheck-maroon > input:first-child:checked + label::before,\n.icheck-maroon > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #d81b60;\n  border-color: #d81b60;\n}\n\n.icheck-blue > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-blue > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #007bff;\n}\n\n.icheck-blue > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-blue > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #007bff;\n}\n\n.icheck-blue > input:first-child:checked + label::before,\n.icheck-blue > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #007bff;\n  border-color: #007bff;\n}\n\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6610f2;\n}\n\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6610f2;\n}\n\n.icheck-indigo > input:first-child:checked + label::before,\n.icheck-indigo > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6610f2;\n  border-color: #6610f2;\n}\n\n.icheck-purple > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-purple > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6f42c1;\n}\n\n.icheck-purple > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-purple > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6f42c1;\n}\n\n.icheck-purple > input:first-child:checked + label::before,\n.icheck-purple > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6f42c1;\n  border-color: #6f42c1;\n}\n\n.icheck-pink > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-pink > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #e83e8c;\n}\n\n.icheck-pink > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-pink > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #e83e8c;\n}\n\n.icheck-pink > input:first-child:checked + label::before,\n.icheck-pink > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #e83e8c;\n  border-color: #e83e8c;\n}\n\n.icheck-red > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-red > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-red > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-red > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #dc3545;\n}\n\n.icheck-red > input:first-child:checked + label::before,\n.icheck-red > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.icheck-orange > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-orange > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #fd7e14;\n}\n\n.icheck-orange > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-orange > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #fd7e14;\n}\n\n.icheck-orange > input:first-child:checked + label::before,\n.icheck-orange > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #fd7e14;\n  border-color: #fd7e14;\n}\n\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #ffc107;\n}\n\n.icheck-yellow > input:first-child:checked + label::before,\n.icheck-yellow > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #ffc107;\n  border-color: #ffc107;\n}\n\n.icheck-green > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-green > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-green > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-green > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #28a745;\n}\n\n.icheck-green > input:first-child:checked + label::before,\n.icheck-green > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #28a745;\n  border-color: #28a745;\n}\n\n.icheck-teal > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-teal > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #20c997;\n}\n\n.icheck-teal > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-teal > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #20c997;\n}\n\n.icheck-teal > input:first-child:checked + label::before,\n.icheck-teal > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #20c997;\n  border-color: #20c997;\n}\n\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #17a2b8;\n}\n\n.icheck-cyan > input:first-child:checked + label::before,\n.icheck-cyan > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n}\n\n.icheck-white > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-white > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #fff;\n}\n\n.icheck-white > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-white > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #fff;\n}\n\n.icheck-white > input:first-child:checked + label::before,\n.icheck-white > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #fff;\n  border-color: #fff;\n}\n\n.icheck-gray > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-gray > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-gray > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-gray > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.icheck-gray > input:first-child:checked + label::before,\n.icheck-gray > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.icheck-gray-dark > input:first-child:checked + label::before,\n.icheck-gray-dark > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.dark-mode [class*=\"icheck-\"] > input:first-child:not(:checked) + input[type=\"hidden\"] + label::before,\n.dark-mode [class*=\"icheck-\"] > input:first-child:not(:checked) + label::before {\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-primary > input:first-child:checked + label::before,\n.dark-mode .icheck-primary > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3f6791;\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-secondary > input:first-child:checked + label::before,\n.dark-mode .icheck-secondary > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-success > input:first-child:checked + label::before,\n.dark-mode .icheck-success > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #00bc8c;\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-info > input:first-child:checked + label::before,\n.dark-mode .icheck-info > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3498db;\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-warning > input:first-child:checked + label::before,\n.dark-mode .icheck-warning > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f39c12;\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-danger > input:first-child:checked + label::before,\n.dark-mode .icheck-danger > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #e74c3c;\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f8f9fa;\n}\n\n.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f8f9fa;\n}\n\n.dark-mode .icheck-light > input:first-child:checked + label::before,\n.dark-mode .icheck-light > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n}\n\n.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.dark-mode .icheck-dark > input:first-child:checked + label::before,\n.dark-mode .icheck-dark > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #86bad8;\n}\n\n.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #86bad8;\n}\n\n.dark-mode .icheck-lightblue > input:first-child:checked + label::before,\n.dark-mode .icheck-lightblue > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #86bad8;\n  border-color: #86bad8;\n}\n\n.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #002c59;\n}\n\n.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #002c59;\n}\n\n.dark-mode .icheck-navy > input:first-child:checked + label::before,\n.dark-mode .icheck-navy > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #002c59;\n  border-color: #002c59;\n}\n\n.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #74c8a3;\n}\n\n.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #74c8a3;\n}\n\n.dark-mode .icheck-olive > input:first-child:checked + label::before,\n.dark-mode .icheck-olive > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #74c8a3;\n  border-color: #74c8a3;\n}\n\n.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #67ffa9;\n}\n\n.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #67ffa9;\n}\n\n.dark-mode .icheck-lime > input:first-child:checked + label::before,\n.dark-mode .icheck-lime > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #67ffa9;\n  border-color: #67ffa9;\n}\n\n.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f672d8;\n}\n\n.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f672d8;\n}\n\n.dark-mode .icheck-fuchsia > input:first-child:checked + label::before,\n.dark-mode .icheck-fuchsia > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f672d8;\n  border-color: #f672d8;\n}\n\n.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #ed6c9b;\n}\n\n.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #ed6c9b;\n}\n\n.dark-mode .icheck-maroon > input:first-child:checked + label::before,\n.dark-mode .icheck-maroon > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #ed6c9b;\n  border-color: #ed6c9b;\n}\n\n.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-blue > input:first-child:checked + label::before,\n.dark-mode .icheck-blue > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3f6791;\n  border-color: #3f6791;\n}\n\n.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6610f2;\n}\n\n.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6610f2;\n}\n\n.dark-mode .icheck-indigo > input:first-child:checked + label::before,\n.dark-mode .icheck-indigo > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6610f2;\n  border-color: #6610f2;\n}\n\n.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6f42c1;\n}\n\n.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6f42c1;\n}\n\n.dark-mode .icheck-purple > input:first-child:checked + label::before,\n.dark-mode .icheck-purple > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6f42c1;\n  border-color: #6f42c1;\n}\n\n.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #e83e8c;\n}\n\n.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #e83e8c;\n}\n\n.dark-mode .icheck-pink > input:first-child:checked + label::before,\n.dark-mode .icheck-pink > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #e83e8c;\n  border-color: #e83e8c;\n}\n\n.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-red > input:first-child:checked + label::before,\n.dark-mode .icheck-red > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #e74c3c;\n  border-color: #e74c3c;\n}\n\n.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #fd7e14;\n}\n\n.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #fd7e14;\n}\n\n.dark-mode .icheck-orange > input:first-child:checked + label::before,\n.dark-mode .icheck-orange > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #fd7e14;\n  border-color: #fd7e14;\n}\n\n.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-yellow > input:first-child:checked + label::before,\n.dark-mode .icheck-yellow > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #f39c12;\n  border-color: #f39c12;\n}\n\n.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-green > input:first-child:checked + label::before,\n.dark-mode .icheck-green > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #00bc8c;\n  border-color: #00bc8c;\n}\n\n.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #20c997;\n}\n\n.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #20c997;\n}\n\n.dark-mode .icheck-teal > input:first-child:checked + label::before,\n.dark-mode .icheck-teal > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #20c997;\n  border-color: #20c997;\n}\n\n.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-cyan > input:first-child:checked + label::before,\n.dark-mode .icheck-cyan > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #3498db;\n  border-color: #3498db;\n}\n\n.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #fff;\n}\n\n.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #fff;\n}\n\n.dark-mode .icheck-white > input:first-child:checked + label::before,\n.dark-mode .icheck-white > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #fff;\n  border-color: #fff;\n}\n\n.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-gray > input:first-child:checked + label::before,\n.dark-mode .icheck-gray > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,\n.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,\n.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n  border-color: #343a40;\n}\n\n.dark-mode .icheck-gray-dark > input:first-child:checked + label::before,\n.dark-mode .icheck-gray-dark > input:first-child:checked + input[type=\"hidden\"] + label::before {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.mapael .map {\n  position: relative;\n}\n\n.mapael .mapTooltip {\n  font-family: \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\";\n  font-style: normal;\n  font-weight: 400;\n  line-height: 1.5;\n  text-align: left;\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n  border-radius: 0.25rem;\n  font-size: 0.875rem;\n  background-color: #000;\n  color: #fff;\n  display: block;\n  max-width: 200px;\n  padding: 0.25rem 0.5rem;\n  position: absolute;\n  text-align: center;\n  word-wrap: break-word;\n  z-index: 1070;\n}\n\n.mapael .myLegend {\n  background-color: #f8f9fa;\n  border: 1px solid #adb5bd;\n  padding: 10px;\n  width: 600px;\n}\n\n.mapael .zoomButton {\n  background-color: #f8f9fa;\n  border: 1px solid #ddd;\n  border-radius: 0.25rem;\n  color: #444;\n  cursor: pointer;\n  font-weight: 700;\n  height: 16px;\n  left: 10px;\n  line-height: 14px;\n  padding-left: 1px;\n  position: absolute;\n  text-align: center;\n  top: 0;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  width: 16px;\n}\n\n.mapael .zoomButton:hover, .mapael .zoomButton:active, .mapael .zoomButton.hover {\n  background-color: #e9ecef;\n  color: #2b2b2b;\n}\n\n.mapael .zoomReset {\n  line-height: 12px;\n  top: 10px;\n}\n\n.mapael .zoomIn {\n  top: 30px;\n}\n\n.mapael .zoomOut {\n  top: 50px;\n}\n\n.jqvmap-zoomin,\n.jqvmap-zoomout {\n  background-color: #f8f9fa;\n  border: 1px solid #ddd;\n  border-radius: 0.25rem;\n  color: #444;\n  height: 15px;\n  width: 15px;\n  padding: 1px 2px;\n}\n\n.jqvmap-zoomin:hover, .jqvmap-zoomin:active, .jqvmap-zoomin.hover,\n.jqvmap-zoomout:hover,\n.jqvmap-zoomout:active,\n.jqvmap-zoomout.hover {\n  background-color: #e9ecef;\n  color: #2b2b2b;\n}\n\n.swal2-icon.swal2-info {\n  border-color: ligthen(#17a2b8, 20%);\n  color: #17a2b8;\n}\n\n.swal2-icon.swal2-warning {\n  border-color: ligthen(#ffc107, 20%);\n  color: #ffc107;\n}\n\n.swal2-icon.swal2-error {\n  border-color: ligthen(#dc3545, 20%);\n  color: #dc3545;\n}\n\n.swal2-icon.swal2-question {\n  border-color: ligthen(#6c757d, 20%);\n  color: #6c757d;\n}\n\n.swal2-icon.swal2-success {\n  border-color: ligthen(#28a745, 20%);\n  color: #28a745;\n}\n\n.swal2-icon.swal2-success .swal2-success-ring {\n  border-color: ligthen(#28a745, 20%);\n}\n\n.swal2-icon.swal2-success [class^='swal2-success-line'] {\n  background-color: #28a745;\n}\n\n.dark-mode .swal2-popup {\n  background-color: #343a40;\n  color: #e9ecef;\n}\n\n.dark-mode .swal2-popup .swal2-content,\n.dark-mode .swal2-popup .swal2-title {\n  color: #e9ecef;\n}\n\n#toast-container .toast {\n  background-color: #007bff;\n}\n\n#toast-container .toast-success {\n  background-color: #28a745;\n}\n\n#toast-container .toast-error {\n  background-color: #dc3545;\n}\n\n#toast-container .toast-info {\n  background-color: #17a2b8;\n}\n\n#toast-container .toast-warning {\n  background-color: #ffc107;\n}\n\n.toast-bottom-full-width .toast,\n.toast-top-full-width .toast {\n  max-width: inherit;\n}\n\n.pace {\n  z-index: 1048;\n}\n\n.pace .pace-progress {\n  z-index: 1049;\n}\n\n.pace .pace-activity {\n  z-index: 1050;\n}\n\n.pace-primary .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-barber-shop-primary .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-primary .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-barber-shop-primary .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-primary .pace .pace-progress::after {\n  color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-bounce-primary .pace .pace-activity {\n  background: #007bff;\n}\n\n.pace-center-atom-primary .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-primary .pace-progress::before {\n  background: #007bff;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-primary .pace-activity {\n  border-color: #007bff;\n}\n\n.pace-center-atom-primary .pace-activity::after, .pace-center-atom-primary .pace-activity::before {\n  border-color: #007bff;\n}\n\n.pace-center-circle-primary .pace .pace-progress {\n  background: rgba(0, 123, 255, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-primary .pace .pace-activity {\n  border-color: #007bff transparent transparent;\n}\n\n.pace-center-radar-primary .pace .pace-activity::before {\n  border-color: #007bff transparent transparent;\n}\n\n.pace-center-simple-primary .pace {\n  background: #fff;\n  border-color: #007bff;\n}\n\n.pace-center-simple-primary .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-material-primary .pace {\n  color: #007bff;\n}\n\n.pace-corner-indicator-primary .pace .pace-activity {\n  background: #007bff;\n}\n\n.pace-corner-indicator-primary .pace .pace-activity::after,\n.pace-corner-indicator-primary .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-primary .pace .pace-activity::before {\n  border-right-color: rgba(0, 123, 255, 0.2);\n  border-left-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-corner-indicator-primary .pace .pace-activity::after {\n  border-top-color: rgba(0, 123, 255, 0.2);\n  border-bottom-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-fill-left-primary .pace .pace-progress {\n  background-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-flash-primary .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-flash-primary .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #007bff, 0 0 5px #007bff;\n}\n\n.pace-flash-primary .pace .pace-activity {\n  border-top-color: #007bff;\n  border-left-color: #007bff;\n}\n\n.pace-loading-bar-primary .pace .pace-progress {\n  background: #007bff;\n  color: #007bff;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-primary .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #007bff, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-primary .pace .pace-progress {\n  background-color: #007bff;\n  box-shadow: inset -1px 0 #007bff, inset 0 -1px #007bff, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-primary .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-primary .pace-progress {\n  color: #007bff;\n}\n\n.pace-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-secondary .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-secondary .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-secondary .pace .pace-progress::after {\n  color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-bounce-secondary .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-center-atom-secondary .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-secondary .pace-progress::before {\n  background: #6c757d;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-secondary .pace-activity {\n  border-color: #6c757d;\n}\n\n.pace-center-atom-secondary .pace-activity::after, .pace-center-atom-secondary .pace-activity::before {\n  border-color: #6c757d;\n}\n\n.pace-center-circle-secondary .pace .pace-progress {\n  background: rgba(108, 117, 125, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-secondary .pace .pace-activity {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-radar-secondary .pace .pace-activity::before {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-simple-secondary .pace {\n  background: #fff;\n  border-color: #6c757d;\n}\n\n.pace-center-simple-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-material-secondary .pace {\n  color: #6c757d;\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity::after,\n.pace-corner-indicator-secondary .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity::before {\n  border-right-color: rgba(108, 117, 125, 0.2);\n  border-left-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-corner-indicator-secondary .pace .pace-activity::after {\n  border-top-color: rgba(108, 117, 125, 0.2);\n  border-bottom-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-fill-left-secondary .pace .pace-progress {\n  background-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-flash-secondary .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-flash-secondary .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6c757d, 0 0 5px #6c757d;\n}\n\n.pace-flash-secondary .pace .pace-activity {\n  border-top-color: #6c757d;\n  border-left-color: #6c757d;\n}\n\n.pace-loading-bar-secondary .pace .pace-progress {\n  background: #6c757d;\n  color: #6c757d;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-secondary .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6c757d, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-secondary .pace .pace-progress {\n  background-color: #6c757d;\n  box-shadow: inset -1px 0 #6c757d, inset 0 -1px #6c757d, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-secondary .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-secondary .pace-progress {\n  color: #6c757d;\n}\n\n.pace-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-success .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-success .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-success .pace .pace-progress::after {\n  color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-bounce-success .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-center-atom-success .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-success .pace-progress::before {\n  background: #28a745;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-success .pace-activity {\n  border-color: #28a745;\n}\n\n.pace-center-atom-success .pace-activity::after, .pace-center-atom-success .pace-activity::before {\n  border-color: #28a745;\n}\n\n.pace-center-circle-success .pace .pace-progress {\n  background: rgba(40, 167, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-success .pace .pace-activity {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-radar-success .pace .pace-activity::before {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-simple-success .pace {\n  background: #fff;\n  border-color: #28a745;\n}\n\n.pace-center-simple-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-material-success .pace {\n  color: #28a745;\n}\n\n.pace-corner-indicator-success .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-corner-indicator-success .pace .pace-activity::after,\n.pace-corner-indicator-success .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-success .pace .pace-activity::before {\n  border-right-color: rgba(40, 167, 69, 0.2);\n  border-left-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-corner-indicator-success .pace .pace-activity::after {\n  border-top-color: rgba(40, 167, 69, 0.2);\n  border-bottom-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-fill-left-success .pace .pace-progress {\n  background-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-flash-success .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-flash-success .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #28a745, 0 0 5px #28a745;\n}\n\n.pace-flash-success .pace .pace-activity {\n  border-top-color: #28a745;\n  border-left-color: #28a745;\n}\n\n.pace-loading-bar-success .pace .pace-progress {\n  background: #28a745;\n  color: #28a745;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-success .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #28a745, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-success .pace .pace-progress {\n  background-color: #28a745;\n  box-shadow: inset -1px 0 #28a745, inset 0 -1px #28a745, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-success .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-success .pace-progress {\n  color: #28a745;\n}\n\n.pace-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-info .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-info .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-info .pace .pace-progress::after {\n  color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-bounce-info .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-center-atom-info .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-info .pace-progress::before {\n  background: #17a2b8;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-info .pace-activity {\n  border-color: #17a2b8;\n}\n\n.pace-center-atom-info .pace-activity::after, .pace-center-atom-info .pace-activity::before {\n  border-color: #17a2b8;\n}\n\n.pace-center-circle-info .pace .pace-progress {\n  background: rgba(23, 162, 184, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-info .pace .pace-activity {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-radar-info .pace .pace-activity::before {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-simple-info .pace {\n  background: #fff;\n  border-color: #17a2b8;\n}\n\n.pace-center-simple-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-material-info .pace {\n  color: #17a2b8;\n}\n\n.pace-corner-indicator-info .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-corner-indicator-info .pace .pace-activity::after,\n.pace-corner-indicator-info .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-info .pace .pace-activity::before {\n  border-right-color: rgba(23, 162, 184, 0.2);\n  border-left-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-corner-indicator-info .pace .pace-activity::after {\n  border-top-color: rgba(23, 162, 184, 0.2);\n  border-bottom-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-fill-left-info .pace .pace-progress {\n  background-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-flash-info .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-flash-info .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #17a2b8, 0 0 5px #17a2b8;\n}\n\n.pace-flash-info .pace .pace-activity {\n  border-top-color: #17a2b8;\n  border-left-color: #17a2b8;\n}\n\n.pace-loading-bar-info .pace .pace-progress {\n  background: #17a2b8;\n  color: #17a2b8;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-info .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #17a2b8, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-info .pace .pace-progress {\n  background-color: #17a2b8;\n  box-shadow: inset -1px 0 #17a2b8, inset 0 -1px #17a2b8, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-info .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-info .pace-progress {\n  color: #17a2b8;\n}\n\n.pace-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-warning .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-warning .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-warning .pace .pace-progress::after {\n  color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-bounce-warning .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-center-atom-warning .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-warning .pace-progress::before {\n  background: #ffc107;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-warning .pace-activity {\n  border-color: #ffc107;\n}\n\n.pace-center-atom-warning .pace-activity::after, .pace-center-atom-warning .pace-activity::before {\n  border-color: #ffc107;\n}\n\n.pace-center-circle-warning .pace .pace-progress {\n  background: rgba(255, 193, 7, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-warning .pace .pace-activity {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-radar-warning .pace .pace-activity::before {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-simple-warning .pace {\n  background: #1f2d3d;\n  border-color: #ffc107;\n}\n\n.pace-center-simple-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-material-warning .pace {\n  color: #ffc107;\n}\n\n.pace-corner-indicator-warning .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-corner-indicator-warning .pace .pace-activity::after,\n.pace-corner-indicator-warning .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-warning .pace .pace-activity::before {\n  border-right-color: rgba(255, 193, 7, 0.2);\n  border-left-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-corner-indicator-warning .pace .pace-activity::after {\n  border-top-color: rgba(255, 193, 7, 0.2);\n  border-bottom-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-fill-left-warning .pace .pace-progress {\n  background-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-flash-warning .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-flash-warning .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #ffc107, 0 0 5px #ffc107;\n}\n\n.pace-flash-warning .pace .pace-activity {\n  border-top-color: #ffc107;\n  border-left-color: #ffc107;\n}\n\n.pace-loading-bar-warning .pace .pace-progress {\n  background: #ffc107;\n  color: #ffc107;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-warning .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #ffc107, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-warning .pace .pace-progress {\n  background-color: #ffc107;\n  box-shadow: inset -1px 0 #ffc107, inset 0 -1px #ffc107, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-warning .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-warning .pace-progress {\n  color: #ffc107;\n}\n\n.pace-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-danger .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-danger .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-danger .pace .pace-progress::after {\n  color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-bounce-danger .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-center-atom-danger .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-danger .pace-progress::before {\n  background: #dc3545;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-danger .pace-activity {\n  border-color: #dc3545;\n}\n\n.pace-center-atom-danger .pace-activity::after, .pace-center-atom-danger .pace-activity::before {\n  border-color: #dc3545;\n}\n\n.pace-center-circle-danger .pace .pace-progress {\n  background: rgba(220, 53, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-danger .pace .pace-activity {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-radar-danger .pace .pace-activity::before {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-simple-danger .pace {\n  background: #fff;\n  border-color: #dc3545;\n}\n\n.pace-center-simple-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-material-danger .pace {\n  color: #dc3545;\n}\n\n.pace-corner-indicator-danger .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-corner-indicator-danger .pace .pace-activity::after,\n.pace-corner-indicator-danger .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-danger .pace .pace-activity::before {\n  border-right-color: rgba(220, 53, 69, 0.2);\n  border-left-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-corner-indicator-danger .pace .pace-activity::after {\n  border-top-color: rgba(220, 53, 69, 0.2);\n  border-bottom-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-fill-left-danger .pace .pace-progress {\n  background-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-flash-danger .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-flash-danger .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #dc3545, 0 0 5px #dc3545;\n}\n\n.pace-flash-danger .pace .pace-activity {\n  border-top-color: #dc3545;\n  border-left-color: #dc3545;\n}\n\n.pace-loading-bar-danger .pace .pace-progress {\n  background: #dc3545;\n  color: #dc3545;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-danger .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #dc3545, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-danger .pace .pace-progress {\n  background-color: #dc3545;\n  box-shadow: inset -1px 0 #dc3545, inset 0 -1px #dc3545, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-danger .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-danger .pace-progress {\n  color: #dc3545;\n}\n\n.pace-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-barber-shop-light .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-barber-shop-light .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-light .pace .pace-progress::after {\n  color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-bounce-light .pace .pace-activity {\n  background: #f8f9fa;\n}\n\n.pace-center-atom-light .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-light .pace-progress::before {\n  background: #f8f9fa;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-light .pace-activity {\n  border-color: #f8f9fa;\n}\n\n.pace-center-atom-light .pace-activity::after, .pace-center-atom-light .pace-activity::before {\n  border-color: #f8f9fa;\n}\n\n.pace-center-circle-light .pace .pace-progress {\n  background: rgba(248, 249, 250, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-light .pace .pace-activity {\n  border-color: #f8f9fa transparent transparent;\n}\n\n.pace-center-radar-light .pace .pace-activity::before {\n  border-color: #f8f9fa transparent transparent;\n}\n\n.pace-center-simple-light .pace {\n  background: #1f2d3d;\n  border-color: #f8f9fa;\n}\n\n.pace-center-simple-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-material-light .pace {\n  color: #f8f9fa;\n}\n\n.pace-corner-indicator-light .pace .pace-activity {\n  background: #f8f9fa;\n}\n\n.pace-corner-indicator-light .pace .pace-activity::after,\n.pace-corner-indicator-light .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-light .pace .pace-activity::before {\n  border-right-color: rgba(248, 249, 250, 0.2);\n  border-left-color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-corner-indicator-light .pace .pace-activity::after {\n  border-top-color: rgba(248, 249, 250, 0.2);\n  border-bottom-color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-fill-left-light .pace .pace-progress {\n  background-color: rgba(248, 249, 250, 0.2);\n}\n\n.pace-flash-light .pace .pace-progress {\n  background: #f8f9fa;\n}\n\n.pace-flash-light .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #f8f9fa, 0 0 5px #f8f9fa;\n}\n\n.pace-flash-light .pace .pace-activity {\n  border-top-color: #f8f9fa;\n  border-left-color: #f8f9fa;\n}\n\n.pace-loading-bar-light .pace .pace-progress {\n  background: #f8f9fa;\n  color: #f8f9fa;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-light .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #f8f9fa, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-light .pace .pace-progress {\n  background-color: #f8f9fa;\n  box-shadow: inset -1px 0 #f8f9fa, inset 0 -1px #f8f9fa, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-light .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-light .pace-progress {\n  color: #f8f9fa;\n}\n\n.pace-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-dark .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-dark .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-dark .pace .pace-progress::after {\n  color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-bounce-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-center-atom-dark .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-dark .pace-progress::before {\n  background: #343a40;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-dark .pace-activity {\n  border-color: #343a40;\n}\n\n.pace-center-atom-dark .pace-activity::after, .pace-center-atom-dark .pace-activity::before {\n  border-color: #343a40;\n}\n\n.pace-center-circle-dark .pace .pace-progress {\n  background: rgba(52, 58, 64, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-dark .pace .pace-activity {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-radar-dark .pace .pace-activity::before {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-simple-dark .pace {\n  background: #fff;\n  border-color: #343a40;\n}\n\n.pace-center-simple-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-material-dark .pace {\n  color: #343a40;\n}\n\n.pace-corner-indicator-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-corner-indicator-dark .pace .pace-activity::after,\n.pace-corner-indicator-dark .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-dark .pace .pace-activity::before {\n  border-right-color: rgba(52, 58, 64, 0.2);\n  border-left-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-corner-indicator-dark .pace .pace-activity::after {\n  border-top-color: rgba(52, 58, 64, 0.2);\n  border-bottom-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-fill-left-dark .pace .pace-progress {\n  background-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-flash-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-flash-dark .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #343a40, 0 0 5px #343a40;\n}\n\n.pace-flash-dark .pace .pace-activity {\n  border-top-color: #343a40;\n  border-left-color: #343a40;\n}\n\n.pace-loading-bar-dark .pace .pace-progress {\n  background: #343a40;\n  color: #343a40;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-dark .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #343a40, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-dark .pace .pace-progress {\n  background-color: #343a40;\n  box-shadow: inset -1px 0 #343a40, inset 0 -1px #343a40, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-dark .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-dark .pace-progress {\n  color: #343a40;\n}\n\n.pace-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-barber-shop-lightblue .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-barber-shop-lightblue .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-lightblue .pace .pace-progress::after {\n  color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-bounce-lightblue .pace .pace-activity {\n  background: #3c8dbc;\n}\n\n.pace-center-atom-lightblue .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-lightblue .pace-progress::before {\n  background: #3c8dbc;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-lightblue .pace-activity {\n  border-color: #3c8dbc;\n}\n\n.pace-center-atom-lightblue .pace-activity::after, .pace-center-atom-lightblue .pace-activity::before {\n  border-color: #3c8dbc;\n}\n\n.pace-center-circle-lightblue .pace .pace-progress {\n  background: rgba(60, 141, 188, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-lightblue .pace .pace-activity {\n  border-color: #3c8dbc transparent transparent;\n}\n\n.pace-center-radar-lightblue .pace .pace-activity::before {\n  border-color: #3c8dbc transparent transparent;\n}\n\n.pace-center-simple-lightblue .pace {\n  background: #fff;\n  border-color: #3c8dbc;\n}\n\n.pace-center-simple-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-material-lightblue .pace {\n  color: #3c8dbc;\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity {\n  background: #3c8dbc;\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity::after,\n.pace-corner-indicator-lightblue .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity::before {\n  border-right-color: rgba(60, 141, 188, 0.2);\n  border-left-color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-corner-indicator-lightblue .pace .pace-activity::after {\n  border-top-color: rgba(60, 141, 188, 0.2);\n  border-bottom-color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-fill-left-lightblue .pace .pace-progress {\n  background-color: rgba(60, 141, 188, 0.2);\n}\n\n.pace-flash-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n}\n\n.pace-flash-lightblue .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #3c8dbc, 0 0 5px #3c8dbc;\n}\n\n.pace-flash-lightblue .pace .pace-activity {\n  border-top-color: #3c8dbc;\n  border-left-color: #3c8dbc;\n}\n\n.pace-loading-bar-lightblue .pace .pace-progress {\n  background: #3c8dbc;\n  color: #3c8dbc;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-lightblue .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #3c8dbc, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-lightblue .pace .pace-progress {\n  background-color: #3c8dbc;\n  box-shadow: inset -1px 0 #3c8dbc, inset 0 -1px #3c8dbc, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-lightblue .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-lightblue .pace-progress {\n  color: #3c8dbc;\n}\n\n.pace-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-barber-shop-navy .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-barber-shop-navy .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-navy .pace .pace-progress::after {\n  color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-bounce-navy .pace .pace-activity {\n  background: #001f3f;\n}\n\n.pace-center-atom-navy .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-navy .pace-progress::before {\n  background: #001f3f;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-navy .pace-activity {\n  border-color: #001f3f;\n}\n\n.pace-center-atom-navy .pace-activity::after, .pace-center-atom-navy .pace-activity::before {\n  border-color: #001f3f;\n}\n\n.pace-center-circle-navy .pace .pace-progress {\n  background: rgba(0, 31, 63, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-navy .pace .pace-activity {\n  border-color: #001f3f transparent transparent;\n}\n\n.pace-center-radar-navy .pace .pace-activity::before {\n  border-color: #001f3f transparent transparent;\n}\n\n.pace-center-simple-navy .pace {\n  background: #fff;\n  border-color: #001f3f;\n}\n\n.pace-center-simple-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-material-navy .pace {\n  color: #001f3f;\n}\n\n.pace-corner-indicator-navy .pace .pace-activity {\n  background: #001f3f;\n}\n\n.pace-corner-indicator-navy .pace .pace-activity::after,\n.pace-corner-indicator-navy .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-navy .pace .pace-activity::before {\n  border-right-color: rgba(0, 31, 63, 0.2);\n  border-left-color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-corner-indicator-navy .pace .pace-activity::after {\n  border-top-color: rgba(0, 31, 63, 0.2);\n  border-bottom-color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-fill-left-navy .pace .pace-progress {\n  background-color: rgba(0, 31, 63, 0.2);\n}\n\n.pace-flash-navy .pace .pace-progress {\n  background: #001f3f;\n}\n\n.pace-flash-navy .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #001f3f, 0 0 5px #001f3f;\n}\n\n.pace-flash-navy .pace .pace-activity {\n  border-top-color: #001f3f;\n  border-left-color: #001f3f;\n}\n\n.pace-loading-bar-navy .pace .pace-progress {\n  background: #001f3f;\n  color: #001f3f;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-navy .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #001f3f, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-navy .pace .pace-progress {\n  background-color: #001f3f;\n  box-shadow: inset -1px 0 #001f3f, inset 0 -1px #001f3f, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-navy .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-navy .pace-progress {\n  color: #001f3f;\n}\n\n.pace-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-barber-shop-olive .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-barber-shop-olive .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-olive .pace .pace-progress::after {\n  color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-bounce-olive .pace .pace-activity {\n  background: #3d9970;\n}\n\n.pace-center-atom-olive .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-olive .pace-progress::before {\n  background: #3d9970;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-olive .pace-activity {\n  border-color: #3d9970;\n}\n\n.pace-center-atom-olive .pace-activity::after, .pace-center-atom-olive .pace-activity::before {\n  border-color: #3d9970;\n}\n\n.pace-center-circle-olive .pace .pace-progress {\n  background: rgba(61, 153, 112, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-olive .pace .pace-activity {\n  border-color: #3d9970 transparent transparent;\n}\n\n.pace-center-radar-olive .pace .pace-activity::before {\n  border-color: #3d9970 transparent transparent;\n}\n\n.pace-center-simple-olive .pace {\n  background: #fff;\n  border-color: #3d9970;\n}\n\n.pace-center-simple-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-material-olive .pace {\n  color: #3d9970;\n}\n\n.pace-corner-indicator-olive .pace .pace-activity {\n  background: #3d9970;\n}\n\n.pace-corner-indicator-olive .pace .pace-activity::after,\n.pace-corner-indicator-olive .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-olive .pace .pace-activity::before {\n  border-right-color: rgba(61, 153, 112, 0.2);\n  border-left-color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-corner-indicator-olive .pace .pace-activity::after {\n  border-top-color: rgba(61, 153, 112, 0.2);\n  border-bottom-color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-fill-left-olive .pace .pace-progress {\n  background-color: rgba(61, 153, 112, 0.2);\n}\n\n.pace-flash-olive .pace .pace-progress {\n  background: #3d9970;\n}\n\n.pace-flash-olive .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #3d9970, 0 0 5px #3d9970;\n}\n\n.pace-flash-olive .pace .pace-activity {\n  border-top-color: #3d9970;\n  border-left-color: #3d9970;\n}\n\n.pace-loading-bar-olive .pace .pace-progress {\n  background: #3d9970;\n  color: #3d9970;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-olive .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #3d9970, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-olive .pace .pace-progress {\n  background-color: #3d9970;\n  box-shadow: inset -1px 0 #3d9970, inset 0 -1px #3d9970, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-olive .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-olive .pace-progress {\n  color: #3d9970;\n}\n\n.pace-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-barber-shop-lime .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-barber-shop-lime .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-lime .pace .pace-progress::after {\n  color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-bounce-lime .pace .pace-activity {\n  background: #01ff70;\n}\n\n.pace-center-atom-lime .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-lime .pace-progress::before {\n  background: #01ff70;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-lime .pace-activity {\n  border-color: #01ff70;\n}\n\n.pace-center-atom-lime .pace-activity::after, .pace-center-atom-lime .pace-activity::before {\n  border-color: #01ff70;\n}\n\n.pace-center-circle-lime .pace .pace-progress {\n  background: rgba(1, 255, 112, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-lime .pace .pace-activity {\n  border-color: #01ff70 transparent transparent;\n}\n\n.pace-center-radar-lime .pace .pace-activity::before {\n  border-color: #01ff70 transparent transparent;\n}\n\n.pace-center-simple-lime .pace {\n  background: #1f2d3d;\n  border-color: #01ff70;\n}\n\n.pace-center-simple-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-material-lime .pace {\n  color: #01ff70;\n}\n\n.pace-corner-indicator-lime .pace .pace-activity {\n  background: #01ff70;\n}\n\n.pace-corner-indicator-lime .pace .pace-activity::after,\n.pace-corner-indicator-lime .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-lime .pace .pace-activity::before {\n  border-right-color: rgba(1, 255, 112, 0.2);\n  border-left-color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-corner-indicator-lime .pace .pace-activity::after {\n  border-top-color: rgba(1, 255, 112, 0.2);\n  border-bottom-color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-fill-left-lime .pace .pace-progress {\n  background-color: rgba(1, 255, 112, 0.2);\n}\n\n.pace-flash-lime .pace .pace-progress {\n  background: #01ff70;\n}\n\n.pace-flash-lime .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #01ff70, 0 0 5px #01ff70;\n}\n\n.pace-flash-lime .pace .pace-activity {\n  border-top-color: #01ff70;\n  border-left-color: #01ff70;\n}\n\n.pace-loading-bar-lime .pace .pace-progress {\n  background: #01ff70;\n  color: #01ff70;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-lime .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #01ff70, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-lime .pace .pace-progress {\n  background-color: #01ff70;\n  box-shadow: inset -1px 0 #01ff70, inset 0 -1px #01ff70, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-lime .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-lime .pace-progress {\n  color: #01ff70;\n}\n\n.pace-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-barber-shop-fuchsia .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-barber-shop-fuchsia .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-fuchsia .pace .pace-progress::after {\n  color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-bounce-fuchsia .pace .pace-activity {\n  background: #f012be;\n}\n\n.pace-center-atom-fuchsia .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-fuchsia .pace-progress::before {\n  background: #f012be;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-fuchsia .pace-activity {\n  border-color: #f012be;\n}\n\n.pace-center-atom-fuchsia .pace-activity::after, .pace-center-atom-fuchsia .pace-activity::before {\n  border-color: #f012be;\n}\n\n.pace-center-circle-fuchsia .pace .pace-progress {\n  background: rgba(240, 18, 190, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-fuchsia .pace .pace-activity {\n  border-color: #f012be transparent transparent;\n}\n\n.pace-center-radar-fuchsia .pace .pace-activity::before {\n  border-color: #f012be transparent transparent;\n}\n\n.pace-center-simple-fuchsia .pace {\n  background: #fff;\n  border-color: #f012be;\n}\n\n.pace-center-simple-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-material-fuchsia .pace {\n  color: #f012be;\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity {\n  background: #f012be;\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity::after,\n.pace-corner-indicator-fuchsia .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity::before {\n  border-right-color: rgba(240, 18, 190, 0.2);\n  border-left-color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-corner-indicator-fuchsia .pace .pace-activity::after {\n  border-top-color: rgba(240, 18, 190, 0.2);\n  border-bottom-color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-fill-left-fuchsia .pace .pace-progress {\n  background-color: rgba(240, 18, 190, 0.2);\n}\n\n.pace-flash-fuchsia .pace .pace-progress {\n  background: #f012be;\n}\n\n.pace-flash-fuchsia .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #f012be, 0 0 5px #f012be;\n}\n\n.pace-flash-fuchsia .pace .pace-activity {\n  border-top-color: #f012be;\n  border-left-color: #f012be;\n}\n\n.pace-loading-bar-fuchsia .pace .pace-progress {\n  background: #f012be;\n  color: #f012be;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-fuchsia .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #f012be, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-fuchsia .pace .pace-progress {\n  background-color: #f012be;\n  box-shadow: inset -1px 0 #f012be, inset 0 -1px #f012be, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-fuchsia .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-fuchsia .pace-progress {\n  color: #f012be;\n}\n\n.pace-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-barber-shop-maroon .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-barber-shop-maroon .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-maroon .pace .pace-progress::after {\n  color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-bounce-maroon .pace .pace-activity {\n  background: #d81b60;\n}\n\n.pace-center-atom-maroon .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-maroon .pace-progress::before {\n  background: #d81b60;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-maroon .pace-activity {\n  border-color: #d81b60;\n}\n\n.pace-center-atom-maroon .pace-activity::after, .pace-center-atom-maroon .pace-activity::before {\n  border-color: #d81b60;\n}\n\n.pace-center-circle-maroon .pace .pace-progress {\n  background: rgba(216, 27, 96, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-maroon .pace .pace-activity {\n  border-color: #d81b60 transparent transparent;\n}\n\n.pace-center-radar-maroon .pace .pace-activity::before {\n  border-color: #d81b60 transparent transparent;\n}\n\n.pace-center-simple-maroon .pace {\n  background: #fff;\n  border-color: #d81b60;\n}\n\n.pace-center-simple-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-material-maroon .pace {\n  color: #d81b60;\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity {\n  background: #d81b60;\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity::after,\n.pace-corner-indicator-maroon .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity::before {\n  border-right-color: rgba(216, 27, 96, 0.2);\n  border-left-color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-corner-indicator-maroon .pace .pace-activity::after {\n  border-top-color: rgba(216, 27, 96, 0.2);\n  border-bottom-color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-fill-left-maroon .pace .pace-progress {\n  background-color: rgba(216, 27, 96, 0.2);\n}\n\n.pace-flash-maroon .pace .pace-progress {\n  background: #d81b60;\n}\n\n.pace-flash-maroon .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #d81b60, 0 0 5px #d81b60;\n}\n\n.pace-flash-maroon .pace .pace-activity {\n  border-top-color: #d81b60;\n  border-left-color: #d81b60;\n}\n\n.pace-loading-bar-maroon .pace .pace-progress {\n  background: #d81b60;\n  color: #d81b60;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-maroon .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #d81b60, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-maroon .pace .pace-progress {\n  background-color: #d81b60;\n  box-shadow: inset -1px 0 #d81b60, inset 0 -1px #d81b60, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-maroon .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-maroon .pace-progress {\n  color: #d81b60;\n}\n\n.pace-blue .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-barber-shop-blue .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-blue .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-barber-shop-blue .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-blue .pace .pace-progress::after {\n  color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-bounce-blue .pace .pace-activity {\n  background: #007bff;\n}\n\n.pace-center-atom-blue .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-blue .pace-progress::before {\n  background: #007bff;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-blue .pace-activity {\n  border-color: #007bff;\n}\n\n.pace-center-atom-blue .pace-activity::after, .pace-center-atom-blue .pace-activity::before {\n  border-color: #007bff;\n}\n\n.pace-center-circle-blue .pace .pace-progress {\n  background: rgba(0, 123, 255, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-blue .pace .pace-activity {\n  border-color: #007bff transparent transparent;\n}\n\n.pace-center-radar-blue .pace .pace-activity::before {\n  border-color: #007bff transparent transparent;\n}\n\n.pace-center-simple-blue .pace {\n  background: #fff;\n  border-color: #007bff;\n}\n\n.pace-center-simple-blue .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-material-blue .pace {\n  color: #007bff;\n}\n\n.pace-corner-indicator-blue .pace .pace-activity {\n  background: #007bff;\n}\n\n.pace-corner-indicator-blue .pace .pace-activity::after,\n.pace-corner-indicator-blue .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-blue .pace .pace-activity::before {\n  border-right-color: rgba(0, 123, 255, 0.2);\n  border-left-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-corner-indicator-blue .pace .pace-activity::after {\n  border-top-color: rgba(0, 123, 255, 0.2);\n  border-bottom-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-fill-left-blue .pace .pace-progress {\n  background-color: rgba(0, 123, 255, 0.2);\n}\n\n.pace-flash-blue .pace .pace-progress {\n  background: #007bff;\n}\n\n.pace-flash-blue .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #007bff, 0 0 5px #007bff;\n}\n\n.pace-flash-blue .pace .pace-activity {\n  border-top-color: #007bff;\n  border-left-color: #007bff;\n}\n\n.pace-loading-bar-blue .pace .pace-progress {\n  background: #007bff;\n  color: #007bff;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-blue .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #007bff, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-blue .pace .pace-progress {\n  background-color: #007bff;\n  box-shadow: inset -1px 0 #007bff, inset 0 -1px #007bff, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-blue .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-blue .pace-progress {\n  color: #007bff;\n}\n\n.pace-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-barber-shop-indigo .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-barber-shop-indigo .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-indigo .pace .pace-progress::after {\n  color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-bounce-indigo .pace .pace-activity {\n  background: #6610f2;\n}\n\n.pace-center-atom-indigo .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-indigo .pace-progress::before {\n  background: #6610f2;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-indigo .pace-activity {\n  border-color: #6610f2;\n}\n\n.pace-center-atom-indigo .pace-activity::after, .pace-center-atom-indigo .pace-activity::before {\n  border-color: #6610f2;\n}\n\n.pace-center-circle-indigo .pace .pace-progress {\n  background: rgba(102, 16, 242, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-indigo .pace .pace-activity {\n  border-color: #6610f2 transparent transparent;\n}\n\n.pace-center-radar-indigo .pace .pace-activity::before {\n  border-color: #6610f2 transparent transparent;\n}\n\n.pace-center-simple-indigo .pace {\n  background: #fff;\n  border-color: #6610f2;\n}\n\n.pace-center-simple-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-material-indigo .pace {\n  color: #6610f2;\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity {\n  background: #6610f2;\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity::after,\n.pace-corner-indicator-indigo .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity::before {\n  border-right-color: rgba(102, 16, 242, 0.2);\n  border-left-color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-corner-indicator-indigo .pace .pace-activity::after {\n  border-top-color: rgba(102, 16, 242, 0.2);\n  border-bottom-color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-fill-left-indigo .pace .pace-progress {\n  background-color: rgba(102, 16, 242, 0.2);\n}\n\n.pace-flash-indigo .pace .pace-progress {\n  background: #6610f2;\n}\n\n.pace-flash-indigo .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6610f2, 0 0 5px #6610f2;\n}\n\n.pace-flash-indigo .pace .pace-activity {\n  border-top-color: #6610f2;\n  border-left-color: #6610f2;\n}\n\n.pace-loading-bar-indigo .pace .pace-progress {\n  background: #6610f2;\n  color: #6610f2;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-indigo .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6610f2, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-indigo .pace .pace-progress {\n  background-color: #6610f2;\n  box-shadow: inset -1px 0 #6610f2, inset 0 -1px #6610f2, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-indigo .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-indigo .pace-progress {\n  color: #6610f2;\n}\n\n.pace-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-barber-shop-purple .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-barber-shop-purple .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-purple .pace .pace-progress::after {\n  color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-bounce-purple .pace .pace-activity {\n  background: #6f42c1;\n}\n\n.pace-center-atom-purple .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-purple .pace-progress::before {\n  background: #6f42c1;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-purple .pace-activity {\n  border-color: #6f42c1;\n}\n\n.pace-center-atom-purple .pace-activity::after, .pace-center-atom-purple .pace-activity::before {\n  border-color: #6f42c1;\n}\n\n.pace-center-circle-purple .pace .pace-progress {\n  background: rgba(111, 66, 193, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-purple .pace .pace-activity {\n  border-color: #6f42c1 transparent transparent;\n}\n\n.pace-center-radar-purple .pace .pace-activity::before {\n  border-color: #6f42c1 transparent transparent;\n}\n\n.pace-center-simple-purple .pace {\n  background: #fff;\n  border-color: #6f42c1;\n}\n\n.pace-center-simple-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-material-purple .pace {\n  color: #6f42c1;\n}\n\n.pace-corner-indicator-purple .pace .pace-activity {\n  background: #6f42c1;\n}\n\n.pace-corner-indicator-purple .pace .pace-activity::after,\n.pace-corner-indicator-purple .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-purple .pace .pace-activity::before {\n  border-right-color: rgba(111, 66, 193, 0.2);\n  border-left-color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-corner-indicator-purple .pace .pace-activity::after {\n  border-top-color: rgba(111, 66, 193, 0.2);\n  border-bottom-color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-fill-left-purple .pace .pace-progress {\n  background-color: rgba(111, 66, 193, 0.2);\n}\n\n.pace-flash-purple .pace .pace-progress {\n  background: #6f42c1;\n}\n\n.pace-flash-purple .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6f42c1, 0 0 5px #6f42c1;\n}\n\n.pace-flash-purple .pace .pace-activity {\n  border-top-color: #6f42c1;\n  border-left-color: #6f42c1;\n}\n\n.pace-loading-bar-purple .pace .pace-progress {\n  background: #6f42c1;\n  color: #6f42c1;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-purple .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6f42c1, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-purple .pace .pace-progress {\n  background-color: #6f42c1;\n  box-shadow: inset -1px 0 #6f42c1, inset 0 -1px #6f42c1, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-purple .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-purple .pace-progress {\n  color: #6f42c1;\n}\n\n.pace-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-barber-shop-pink .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-barber-shop-pink .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-pink .pace .pace-progress::after {\n  color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-bounce-pink .pace .pace-activity {\n  background: #e83e8c;\n}\n\n.pace-center-atom-pink .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-pink .pace-progress::before {\n  background: #e83e8c;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-pink .pace-activity {\n  border-color: #e83e8c;\n}\n\n.pace-center-atom-pink .pace-activity::after, .pace-center-atom-pink .pace-activity::before {\n  border-color: #e83e8c;\n}\n\n.pace-center-circle-pink .pace .pace-progress {\n  background: rgba(232, 62, 140, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-pink .pace .pace-activity {\n  border-color: #e83e8c transparent transparent;\n}\n\n.pace-center-radar-pink .pace .pace-activity::before {\n  border-color: #e83e8c transparent transparent;\n}\n\n.pace-center-simple-pink .pace {\n  background: #fff;\n  border-color: #e83e8c;\n}\n\n.pace-center-simple-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-material-pink .pace {\n  color: #e83e8c;\n}\n\n.pace-corner-indicator-pink .pace .pace-activity {\n  background: #e83e8c;\n}\n\n.pace-corner-indicator-pink .pace .pace-activity::after,\n.pace-corner-indicator-pink .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-pink .pace .pace-activity::before {\n  border-right-color: rgba(232, 62, 140, 0.2);\n  border-left-color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-corner-indicator-pink .pace .pace-activity::after {\n  border-top-color: rgba(232, 62, 140, 0.2);\n  border-bottom-color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-fill-left-pink .pace .pace-progress {\n  background-color: rgba(232, 62, 140, 0.2);\n}\n\n.pace-flash-pink .pace .pace-progress {\n  background: #e83e8c;\n}\n\n.pace-flash-pink .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #e83e8c, 0 0 5px #e83e8c;\n}\n\n.pace-flash-pink .pace .pace-activity {\n  border-top-color: #e83e8c;\n  border-left-color: #e83e8c;\n}\n\n.pace-loading-bar-pink .pace .pace-progress {\n  background: #e83e8c;\n  color: #e83e8c;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-pink .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #e83e8c, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-pink .pace .pace-progress {\n  background-color: #e83e8c;\n  box-shadow: inset -1px 0 #e83e8c, inset 0 -1px #e83e8c, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-pink .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-pink .pace-progress {\n  color: #e83e8c;\n}\n\n.pace-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-red .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-barber-shop-red .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-red .pace .pace-progress::after {\n  color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-bounce-red .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-center-atom-red .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-red .pace-progress::before {\n  background: #dc3545;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-red .pace-activity {\n  border-color: #dc3545;\n}\n\n.pace-center-atom-red .pace-activity::after, .pace-center-atom-red .pace-activity::before {\n  border-color: #dc3545;\n}\n\n.pace-center-circle-red .pace .pace-progress {\n  background: rgba(220, 53, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-red .pace .pace-activity {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-radar-red .pace .pace-activity::before {\n  border-color: #dc3545 transparent transparent;\n}\n\n.pace-center-simple-red .pace {\n  background: #fff;\n  border-color: #dc3545;\n}\n\n.pace-center-simple-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-material-red .pace {\n  color: #dc3545;\n}\n\n.pace-corner-indicator-red .pace .pace-activity {\n  background: #dc3545;\n}\n\n.pace-corner-indicator-red .pace .pace-activity::after,\n.pace-corner-indicator-red .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-red .pace .pace-activity::before {\n  border-right-color: rgba(220, 53, 69, 0.2);\n  border-left-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-corner-indicator-red .pace .pace-activity::after {\n  border-top-color: rgba(220, 53, 69, 0.2);\n  border-bottom-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-fill-left-red .pace .pace-progress {\n  background-color: rgba(220, 53, 69, 0.2);\n}\n\n.pace-flash-red .pace .pace-progress {\n  background: #dc3545;\n}\n\n.pace-flash-red .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #dc3545, 0 0 5px #dc3545;\n}\n\n.pace-flash-red .pace .pace-activity {\n  border-top-color: #dc3545;\n  border-left-color: #dc3545;\n}\n\n.pace-loading-bar-red .pace .pace-progress {\n  background: #dc3545;\n  color: #dc3545;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-red .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #dc3545, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-red .pace .pace-progress {\n  background-color: #dc3545;\n  box-shadow: inset -1px 0 #dc3545, inset 0 -1px #dc3545, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-red .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-red .pace-progress {\n  color: #dc3545;\n}\n\n.pace-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-barber-shop-orange .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-barber-shop-orange .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-orange .pace .pace-progress::after {\n  color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-bounce-orange .pace .pace-activity {\n  background: #fd7e14;\n}\n\n.pace-center-atom-orange .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-orange .pace-progress::before {\n  background: #fd7e14;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-orange .pace-activity {\n  border-color: #fd7e14;\n}\n\n.pace-center-atom-orange .pace-activity::after, .pace-center-atom-orange .pace-activity::before {\n  border-color: #fd7e14;\n}\n\n.pace-center-circle-orange .pace .pace-progress {\n  background: rgba(253, 126, 20, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-orange .pace .pace-activity {\n  border-color: #fd7e14 transparent transparent;\n}\n\n.pace-center-radar-orange .pace .pace-activity::before {\n  border-color: #fd7e14 transparent transparent;\n}\n\n.pace-center-simple-orange .pace {\n  background: #1f2d3d;\n  border-color: #fd7e14;\n}\n\n.pace-center-simple-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-material-orange .pace {\n  color: #fd7e14;\n}\n\n.pace-corner-indicator-orange .pace .pace-activity {\n  background: #fd7e14;\n}\n\n.pace-corner-indicator-orange .pace .pace-activity::after,\n.pace-corner-indicator-orange .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-orange .pace .pace-activity::before {\n  border-right-color: rgba(253, 126, 20, 0.2);\n  border-left-color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-corner-indicator-orange .pace .pace-activity::after {\n  border-top-color: rgba(253, 126, 20, 0.2);\n  border-bottom-color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-fill-left-orange .pace .pace-progress {\n  background-color: rgba(253, 126, 20, 0.2);\n}\n\n.pace-flash-orange .pace .pace-progress {\n  background: #fd7e14;\n}\n\n.pace-flash-orange .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #fd7e14, 0 0 5px #fd7e14;\n}\n\n.pace-flash-orange .pace .pace-activity {\n  border-top-color: #fd7e14;\n  border-left-color: #fd7e14;\n}\n\n.pace-loading-bar-orange .pace .pace-progress {\n  background: #fd7e14;\n  color: #fd7e14;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-orange .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #fd7e14, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-orange .pace .pace-progress {\n  background-color: #fd7e14;\n  box-shadow: inset -1px 0 #fd7e14, inset 0 -1px #fd7e14, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-orange .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-orange .pace-progress {\n  color: #fd7e14;\n}\n\n.pace-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-yellow .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-barber-shop-yellow .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-yellow .pace .pace-progress::after {\n  color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-bounce-yellow .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-center-atom-yellow .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-yellow .pace-progress::before {\n  background: #ffc107;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-yellow .pace-activity {\n  border-color: #ffc107;\n}\n\n.pace-center-atom-yellow .pace-activity::after, .pace-center-atom-yellow .pace-activity::before {\n  border-color: #ffc107;\n}\n\n.pace-center-circle-yellow .pace .pace-progress {\n  background: rgba(255, 193, 7, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-yellow .pace .pace-activity {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-radar-yellow .pace .pace-activity::before {\n  border-color: #ffc107 transparent transparent;\n}\n\n.pace-center-simple-yellow .pace {\n  background: #1f2d3d;\n  border-color: #ffc107;\n}\n\n.pace-center-simple-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-material-yellow .pace {\n  color: #ffc107;\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity {\n  background: #ffc107;\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity::after,\n.pace-corner-indicator-yellow .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity::before {\n  border-right-color: rgba(255, 193, 7, 0.2);\n  border-left-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-corner-indicator-yellow .pace .pace-activity::after {\n  border-top-color: rgba(255, 193, 7, 0.2);\n  border-bottom-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-fill-left-yellow .pace .pace-progress {\n  background-color: rgba(255, 193, 7, 0.2);\n}\n\n.pace-flash-yellow .pace .pace-progress {\n  background: #ffc107;\n}\n\n.pace-flash-yellow .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #ffc107, 0 0 5px #ffc107;\n}\n\n.pace-flash-yellow .pace .pace-activity {\n  border-top-color: #ffc107;\n  border-left-color: #ffc107;\n}\n\n.pace-loading-bar-yellow .pace .pace-progress {\n  background: #ffc107;\n  color: #ffc107;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-yellow .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #ffc107, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-yellow .pace .pace-progress {\n  background-color: #ffc107;\n  box-shadow: inset -1px 0 #ffc107, inset 0 -1px #ffc107, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-yellow .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-yellow .pace-progress {\n  color: #ffc107;\n}\n\n.pace-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-green .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-barber-shop-green .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-green .pace .pace-progress::after {\n  color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-bounce-green .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-center-atom-green .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-green .pace-progress::before {\n  background: #28a745;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-green .pace-activity {\n  border-color: #28a745;\n}\n\n.pace-center-atom-green .pace-activity::after, .pace-center-atom-green .pace-activity::before {\n  border-color: #28a745;\n}\n\n.pace-center-circle-green .pace .pace-progress {\n  background: rgba(40, 167, 69, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-green .pace .pace-activity {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-radar-green .pace .pace-activity::before {\n  border-color: #28a745 transparent transparent;\n}\n\n.pace-center-simple-green .pace {\n  background: #fff;\n  border-color: #28a745;\n}\n\n.pace-center-simple-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-material-green .pace {\n  color: #28a745;\n}\n\n.pace-corner-indicator-green .pace .pace-activity {\n  background: #28a745;\n}\n\n.pace-corner-indicator-green .pace .pace-activity::after,\n.pace-corner-indicator-green .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-green .pace .pace-activity::before {\n  border-right-color: rgba(40, 167, 69, 0.2);\n  border-left-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-corner-indicator-green .pace .pace-activity::after {\n  border-top-color: rgba(40, 167, 69, 0.2);\n  border-bottom-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-fill-left-green .pace .pace-progress {\n  background-color: rgba(40, 167, 69, 0.2);\n}\n\n.pace-flash-green .pace .pace-progress {\n  background: #28a745;\n}\n\n.pace-flash-green .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #28a745, 0 0 5px #28a745;\n}\n\n.pace-flash-green .pace .pace-activity {\n  border-top-color: #28a745;\n  border-left-color: #28a745;\n}\n\n.pace-loading-bar-green .pace .pace-progress {\n  background: #28a745;\n  color: #28a745;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-green .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #28a745, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-green .pace .pace-progress {\n  background-color: #28a745;\n  box-shadow: inset -1px 0 #28a745, inset 0 -1px #28a745, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-green .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-green .pace-progress {\n  color: #28a745;\n}\n\n.pace-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-barber-shop-teal .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-barber-shop-teal .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-teal .pace .pace-progress::after {\n  color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-bounce-teal .pace .pace-activity {\n  background: #20c997;\n}\n\n.pace-center-atom-teal .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-teal .pace-progress::before {\n  background: #20c997;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-teal .pace-activity {\n  border-color: #20c997;\n}\n\n.pace-center-atom-teal .pace-activity::after, .pace-center-atom-teal .pace-activity::before {\n  border-color: #20c997;\n}\n\n.pace-center-circle-teal .pace .pace-progress {\n  background: rgba(32, 201, 151, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-teal .pace .pace-activity {\n  border-color: #20c997 transparent transparent;\n}\n\n.pace-center-radar-teal .pace .pace-activity::before {\n  border-color: #20c997 transparent transparent;\n}\n\n.pace-center-simple-teal .pace {\n  background: #fff;\n  border-color: #20c997;\n}\n\n.pace-center-simple-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-material-teal .pace {\n  color: #20c997;\n}\n\n.pace-corner-indicator-teal .pace .pace-activity {\n  background: #20c997;\n}\n\n.pace-corner-indicator-teal .pace .pace-activity::after,\n.pace-corner-indicator-teal .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-teal .pace .pace-activity::before {\n  border-right-color: rgba(32, 201, 151, 0.2);\n  border-left-color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-corner-indicator-teal .pace .pace-activity::after {\n  border-top-color: rgba(32, 201, 151, 0.2);\n  border-bottom-color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-fill-left-teal .pace .pace-progress {\n  background-color: rgba(32, 201, 151, 0.2);\n}\n\n.pace-flash-teal .pace .pace-progress {\n  background: #20c997;\n}\n\n.pace-flash-teal .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #20c997, 0 0 5px #20c997;\n}\n\n.pace-flash-teal .pace .pace-activity {\n  border-top-color: #20c997;\n  border-left-color: #20c997;\n}\n\n.pace-loading-bar-teal .pace .pace-progress {\n  background: #20c997;\n  color: #20c997;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-teal .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #20c997, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-teal .pace .pace-progress {\n  background-color: #20c997;\n  box-shadow: inset -1px 0 #20c997, inset 0 -1px #20c997, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-teal .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-teal .pace-progress {\n  color: #20c997;\n}\n\n.pace-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-cyan .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-barber-shop-cyan .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-cyan .pace .pace-progress::after {\n  color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-bounce-cyan .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-center-atom-cyan .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-cyan .pace-progress::before {\n  background: #17a2b8;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-cyan .pace-activity {\n  border-color: #17a2b8;\n}\n\n.pace-center-atom-cyan .pace-activity::after, .pace-center-atom-cyan .pace-activity::before {\n  border-color: #17a2b8;\n}\n\n.pace-center-circle-cyan .pace .pace-progress {\n  background: rgba(23, 162, 184, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-cyan .pace .pace-activity {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-radar-cyan .pace .pace-activity::before {\n  border-color: #17a2b8 transparent transparent;\n}\n\n.pace-center-simple-cyan .pace {\n  background: #fff;\n  border-color: #17a2b8;\n}\n\n.pace-center-simple-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-material-cyan .pace {\n  color: #17a2b8;\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity {\n  background: #17a2b8;\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity::after,\n.pace-corner-indicator-cyan .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity::before {\n  border-right-color: rgba(23, 162, 184, 0.2);\n  border-left-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-corner-indicator-cyan .pace .pace-activity::after {\n  border-top-color: rgba(23, 162, 184, 0.2);\n  border-bottom-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-fill-left-cyan .pace .pace-progress {\n  background-color: rgba(23, 162, 184, 0.2);\n}\n\n.pace-flash-cyan .pace .pace-progress {\n  background: #17a2b8;\n}\n\n.pace-flash-cyan .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #17a2b8, 0 0 5px #17a2b8;\n}\n\n.pace-flash-cyan .pace .pace-activity {\n  border-top-color: #17a2b8;\n  border-left-color: #17a2b8;\n}\n\n.pace-loading-bar-cyan .pace .pace-progress {\n  background: #17a2b8;\n  color: #17a2b8;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-cyan .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #17a2b8, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-cyan .pace .pace-progress {\n  background-color: #17a2b8;\n  box-shadow: inset -1px 0 #17a2b8, inset 0 -1px #17a2b8, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-cyan .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-cyan .pace-progress {\n  color: #17a2b8;\n}\n\n.pace-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-barber-shop-white .pace {\n  background: #1f2d3d;\n}\n\n.pace-barber-shop-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-barber-shop-white .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-white .pace .pace-progress::after {\n  color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-bounce-white .pace .pace-activity {\n  background: #fff;\n}\n\n.pace-center-atom-white .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-white .pace-progress::before {\n  background: #fff;\n  color: #1f2d3d;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-white .pace-activity {\n  border-color: #fff;\n}\n\n.pace-center-atom-white .pace-activity::after, .pace-center-atom-white .pace-activity::before {\n  border-color: #fff;\n}\n\n.pace-center-circle-white .pace .pace-progress {\n  background: rgba(255, 255, 255, 0.8);\n  color: #1f2d3d;\n}\n\n.pace-center-radar-white .pace .pace-activity {\n  border-color: #fff transparent transparent;\n}\n\n.pace-center-radar-white .pace .pace-activity::before {\n  border-color: #fff transparent transparent;\n}\n\n.pace-center-simple-white .pace {\n  background: #1f2d3d;\n  border-color: #fff;\n}\n\n.pace-center-simple-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-material-white .pace {\n  color: #fff;\n}\n\n.pace-corner-indicator-white .pace .pace-activity {\n  background: #fff;\n}\n\n.pace-corner-indicator-white .pace .pace-activity::after,\n.pace-corner-indicator-white .pace .pace-activity::before {\n  border: 5px solid #1f2d3d;\n}\n\n.pace-corner-indicator-white .pace .pace-activity::before {\n  border-right-color: rgba(255, 255, 255, 0.2);\n  border-left-color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-corner-indicator-white .pace .pace-activity::after {\n  border-top-color: rgba(255, 255, 255, 0.2);\n  border-bottom-color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-fill-left-white .pace .pace-progress {\n  background-color: rgba(255, 255, 255, 0.2);\n}\n\n.pace-flash-white .pace .pace-progress {\n  background: #fff;\n}\n\n.pace-flash-white .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #fff, 0 0 5px #fff;\n}\n\n.pace-flash-white .pace .pace-activity {\n  border-top-color: #fff;\n  border-left-color: #fff;\n}\n\n.pace-loading-bar-white .pace .pace-progress {\n  background: #fff;\n  color: #fff;\n  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;\n}\n\n.pace-loading-bar-white .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #fff, inset 0 0 0 7px #1f2d3d;\n}\n\n.pace-mac-osx-white .pace .pace-progress {\n  background-color: #fff;\n  box-shadow: inset -1px 0 #fff, inset 0 -1px #fff, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);\n}\n\n.pace-mac-osx-white .pace .pace-activity {\n  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-white .pace-progress {\n  color: #fff;\n}\n\n.pace-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-gray .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-barber-shop-gray .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-gray .pace .pace-progress::after {\n  color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-bounce-gray .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-center-atom-gray .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-gray .pace-progress::before {\n  background: #6c757d;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-gray .pace-activity {\n  border-color: #6c757d;\n}\n\n.pace-center-atom-gray .pace-activity::after, .pace-center-atom-gray .pace-activity::before {\n  border-color: #6c757d;\n}\n\n.pace-center-circle-gray .pace .pace-progress {\n  background: rgba(108, 117, 125, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-gray .pace .pace-activity {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-radar-gray .pace .pace-activity::before {\n  border-color: #6c757d transparent transparent;\n}\n\n.pace-center-simple-gray .pace {\n  background: #fff;\n  border-color: #6c757d;\n}\n\n.pace-center-simple-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-material-gray .pace {\n  color: #6c757d;\n}\n\n.pace-corner-indicator-gray .pace .pace-activity {\n  background: #6c757d;\n}\n\n.pace-corner-indicator-gray .pace .pace-activity::after,\n.pace-corner-indicator-gray .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-gray .pace .pace-activity::before {\n  border-right-color: rgba(108, 117, 125, 0.2);\n  border-left-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-corner-indicator-gray .pace .pace-activity::after {\n  border-top-color: rgba(108, 117, 125, 0.2);\n  border-bottom-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-fill-left-gray .pace .pace-progress {\n  background-color: rgba(108, 117, 125, 0.2);\n}\n\n.pace-flash-gray .pace .pace-progress {\n  background: #6c757d;\n}\n\n.pace-flash-gray .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #6c757d, 0 0 5px #6c757d;\n}\n\n.pace-flash-gray .pace .pace-activity {\n  border-top-color: #6c757d;\n  border-left-color: #6c757d;\n}\n\n.pace-loading-bar-gray .pace .pace-progress {\n  background: #6c757d;\n  color: #6c757d;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-gray .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #6c757d, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-gray .pace .pace-progress {\n  background-color: #6c757d;\n  box-shadow: inset -1px 0 #6c757d, inset 0 -1px #6c757d, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-gray .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-gray .pace-progress {\n  color: #6c757d;\n}\n\n.pace-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-gray-dark .pace {\n  background: #fff;\n}\n\n.pace-barber-shop-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-barber-shop-gray-dark .pace .pace-activity {\n  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);\n}\n\n.pace-big-counter-gray-dark .pace .pace-progress::after {\n  color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-bounce-gray-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-center-atom-gray-dark .pace-progress {\n  height: 100px;\n  width: 80px;\n}\n\n.pace-center-atom-gray-dark .pace-progress::before {\n  background: #343a40;\n  color: #fff;\n  font-size: .8rem;\n  line-height: .7rem;\n  padding-top: 17%;\n}\n\n.pace-center-atom-gray-dark .pace-activity {\n  border-color: #343a40;\n}\n\n.pace-center-atom-gray-dark .pace-activity::after, .pace-center-atom-gray-dark .pace-activity::before {\n  border-color: #343a40;\n}\n\n.pace-center-circle-gray-dark .pace .pace-progress {\n  background: rgba(52, 58, 64, 0.8);\n  color: #fff;\n}\n\n.pace-center-radar-gray-dark .pace .pace-activity {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-radar-gray-dark .pace .pace-activity::before {\n  border-color: #343a40 transparent transparent;\n}\n\n.pace-center-simple-gray-dark .pace {\n  background: #fff;\n  border-color: #343a40;\n}\n\n.pace-center-simple-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-material-gray-dark .pace {\n  color: #343a40;\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity {\n  background: #343a40;\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity::after,\n.pace-corner-indicator-gray-dark .pace .pace-activity::before {\n  border: 5px solid #fff;\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity::before {\n  border-right-color: rgba(52, 58, 64, 0.2);\n  border-left-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-corner-indicator-gray-dark .pace .pace-activity::after {\n  border-top-color: rgba(52, 58, 64, 0.2);\n  border-bottom-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-fill-left-gray-dark .pace .pace-progress {\n  background-color: rgba(52, 58, 64, 0.2);\n}\n\n.pace-flash-gray-dark .pace .pace-progress {\n  background: #343a40;\n}\n\n.pace-flash-gray-dark .pace .pace-progress-inner {\n  box-shadow: 0 0 10px #343a40, 0 0 5px #343a40;\n}\n\n.pace-flash-gray-dark .pace .pace-activity {\n  border-top-color: #343a40;\n  border-left-color: #343a40;\n}\n\n.pace-loading-bar-gray-dark .pace .pace-progress {\n  background: #343a40;\n  color: #343a40;\n  box-shadow: 120px 0 #fff, 240px 0 #fff;\n}\n\n.pace-loading-bar-gray-dark .pace .pace-activity {\n  box-shadow: inset 0 0 0 2px #343a40, inset 0 0 0 7px #fff;\n}\n\n.pace-mac-osx-gray-dark .pace .pace-progress {\n  background-color: #343a40;\n  box-shadow: inset -1px 0 #343a40, inset 0 -1px #343a40, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);\n}\n\n.pace-mac-osx-gray-dark .pace .pace-activity {\n  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);\n  height: 12px;\n}\n\n.pace-progress-color-gray-dark .pace-progress {\n  color: #343a40;\n}\n\n/**\n  * bootstrap-switch - Turn checkboxes and radio buttons into toggle switches.\n  *\n  * @version v3.4 (MODDED)\n  * @homepage https://bttstrp.github.io/bootstrap-switch\n  * <AUTHOR> Larentis <<EMAIL>> (http://larentis.eu)\n  * @license MIT\n  */\n.bootstrap-switch {\n  border: 1px solid #ced4da;\n  border-radius: 0.25rem;\n  cursor: pointer;\n  direction: ltr;\n  display: inline-block;\n  line-height: .5rem;\n  overflow: hidden;\n  position: relative;\n  text-align: left;\n  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  vertical-align: middle;\n  z-index: 0;\n}\n\n.bootstrap-switch .bootstrap-switch-container {\n  border-radius: 0.25rem;\n  display: inline-block;\n  top: 0;\n  -webkit-transform: translate3d(0, 0, 0);\n  transform: translate3d(0, 0, 0);\n}\n\n.bootstrap-switch:focus-within {\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on,\n.bootstrap-switch .bootstrap-switch-handle-off,\n.bootstrap-switch .bootstrap-switch-label {\n  box-sizing: border-box;\n  cursor: pointer;\n  display: table-cell;\n  font-size: 1rem;\n  font-weight: 500;\n  line-height: 1.2rem;\n  padding: .25rem .5rem;\n  vertical-align: middle;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on,\n.bootstrap-switch .bootstrap-switch-handle-off {\n  text-align: center;\n  z-index: 1;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default {\n  background: #e9ecef;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary {\n  background: #007bff;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-secondary,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-secondary {\n  background: #6c757d;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success {\n  background: #28a745;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info {\n  background: #17a2b8;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning {\n  background: #ffc107;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger {\n  background: #dc3545;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-light,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-light {\n  background: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-dark,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-dark {\n  background: #343a40;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lightblue,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lightblue {\n  background: #3c8dbc;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-navy,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-navy {\n  background: #001f3f;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-olive,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-olive {\n  background: #3d9970;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lime,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lime {\n  background: #01ff70;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-fuchsia,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-fuchsia {\n  background: #f012be;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-maroon,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-maroon {\n  background: #d81b60;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-blue,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-blue {\n  background: #007bff;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-indigo,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-indigo {\n  background: #6610f2;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-purple,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-purple {\n  background: #6f42c1;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-pink,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-pink {\n  background: #e83e8c;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-red,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-red {\n  background: #dc3545;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-orange,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-orange {\n  background: #fd7e14;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-yellow,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-yellow {\n  background: #ffc107;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-green,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-green {\n  background: #28a745;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-teal,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-teal {\n  background: #20c997;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-cyan,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-cyan {\n  background: #17a2b8;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-white,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-white {\n  background: #fff;\n  color: #1f2d3d;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray {\n  background: #6c757d;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray-dark,\n.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray-dark {\n  background: #343a40;\n  color: #fff;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-on {\n  border-bottom-left-radius: 0.1rem;\n  border-top-left-radius: 0.1rem;\n}\n\n.bootstrap-switch .bootstrap-switch-handle-off {\n  border-bottom-right-radius: 0.1rem;\n  border-top-right-radius: 0.1rem;\n}\n\n.bootstrap-switch input[type='radio'],\n.bootstrap-switch input[type='checkbox'] {\n  filter: alpha(opacity=0);\n  left: 0;\n  margin: 0;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  visibility: hidden;\n  z-index: -1;\n}\n\n.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-label {\n  font-size: .875rem;\n  line-height: 1.5;\n  padding: .1rem .3rem;\n}\n\n.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-label {\n  font-size: .875rem;\n  line-height: 1.5;\n  padding: .2rem .4rem;\n}\n\n.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-label {\n  font-size: 1.25rem;\n  line-height: 1.3333333rem;\n  padding: .3rem .5rem;\n}\n\n.bootstrap-switch.bootstrap-switch-disabled, .bootstrap-switch.bootstrap-switch-readonly, .bootstrap-switch.bootstrap-switch-indeterminate {\n  cursor: default;\n}\n\n.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-label, .bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-label, .bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-on,\n.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-off,\n.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-label {\n  cursor: default;\n  filter: alpha(opacity=50);\n  opacity: .5;\n}\n\n.bootstrap-switch.bootstrap-switch-animate .bootstrap-switch-container {\n  transition: margin-left .5s;\n}\n\n.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-on {\n  border-radius: 0 0.1rem 0.1rem 0;\n}\n\n.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-off {\n  border-radius: 0.1rem 0 0 0.1rem;\n}\n\n.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-label,\n.bootstrap-switch.bootstrap-switch-inverse.bootstrap-switch-off .bootstrap-switch-label {\n  border-bottom-right-radius: 0.1rem;\n  border-top-right-radius: 0.1rem;\n}\n\n.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-label,\n.bootstrap-switch.bootstrap-switch-inverse.bootstrap-switch-on .bootstrap-switch-label {\n  border-bottom-left-radius: 0.1rem;\n  border-top-left-radius: 0.1rem;\n}\n\n.dark-mode .bootstrap-switch {\n  border-color: #6c757d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default {\n  background-color: #3a4047;\n  color: #fff;\n  border-color: #454d55;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary {\n  background: #3f6791;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-secondary,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-secondary {\n  background: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success {\n  background: #00bc8c;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info {\n  background: #3498db;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning {\n  background: #f39c12;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger {\n  background: #e74c3c;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-light,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-light {\n  background: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-dark,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-dark {\n  background: #343a40;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lightblue,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lightblue {\n  background: #86bad8;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-navy,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-navy {\n  background: #002c59;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-olive,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-olive {\n  background: #74c8a3;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lime,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lime {\n  background: #67ffa9;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-fuchsia,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-fuchsia {\n  background: #f672d8;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-maroon,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-maroon {\n  background: #ed6c9b;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-blue,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-blue {\n  background: #3f6791;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-indigo,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-indigo {\n  background: #6610f2;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-purple,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-purple {\n  background: #6f42c1;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-pink,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-pink {\n  background: #e83e8c;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-red,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-red {\n  background: #e74c3c;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-orange,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-orange {\n  background: #fd7e14;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-yellow,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-yellow {\n  background: #f39c12;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-green,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-green {\n  background: #00bc8c;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-teal,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-teal {\n  background: #20c997;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-cyan,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-cyan {\n  background: #3498db;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-white,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-white {\n  background: #fff;\n  color: #1f2d3d;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray {\n  background: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray-dark,\n.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray-dark {\n  background: #343a40;\n  color: #fff;\n}\n\n.dark-mode .daterangepicker {\n  background-color: #3f474e;\n  border: inherit;\n}\n\n.dark-mode .daterangepicker::before, .dark-mode .daterangepicker::after {\n  border-bottom-color: #3f474e;\n}\n\n.dark-mode .daterangepicker td.available:hover,\n.dark-mode .daterangepicker th.available:hover {\n  background-color: #3f474e;\n}\n\n.dark-mode .daterangepicker td.in-range {\n  background-color: #4b545c;\n  color: #fff;\n}\n\n.dark-mode .daterangepicker td.off,\n.dark-mode .daterangepicker td.off.in-range,\n.dark-mode .daterangepicker td.off.start-date,\n.dark-mode .daterangepicker td.off.end-date {\n  background-color: #292d32;\n  color: #fff;\n}\n\n.dark-mode .daterangepicker .ranges li:hover {\n  background-color: #343a40;\n}\n\n.dark-mode .daterangepicker.show-ranges.ltr .drp-calendar {\n  border-color: #4b545c;\n}\n\n.dark-mode .daterangepicker.show-ranges.ltr .drp-calendar.left, .dark-mode .daterangepicker.show-ranges.ltr .drp-calendar.right {\n  border-color: #4b545c;\n  padding-top: 0;\n}\n\n.dark-mode .daterangepicker .drp-buttons {\n  border-color: #4b545c;\n}\n\n.dark-mode .daterangepicker .calendar-table {\n  background-color: #343a40;\n  border-color: #4b545c;\n}\n\n.dark-mode .daterangepicker .calendar-table th,\n.dark-mode .daterangepicker .calendar-table td {\n  color: #fff;\n}\n\n.dark-mode .daterangepicker .calendar-table .next span,\n.dark-mode .daterangepicker .calendar-table .prev span {\n  border-color: #fff;\n}\n\n.dark-mode .daterangepicker select.hourselect,\n.dark-mode .daterangepicker select.minuteselect,\n.dark-mode .daterangepicker select.secondselect,\n.dark-mode .daterangepicker select.ampmselect {\n  background-color: #343a40;\n  border-color: #4b545c;\n}\n\n.jqstooltip {\n  height: auto !important;\n  padding: 5px !important;\n  width: auto !important;\n}\n\n.connectedSortable {\n  min-height: 100px;\n}\n\n.ui-helper-hidden-accessible {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sort-highlight {\n  background: #f8f9fa;\n  border: 1px dashed #dee2e6;\n  margin-bottom: 10px;\n}\n\n.chart {\n  overflow: hidden;\n  position: relative;\n}\n\n.dark-mode .irs--flat .irs-line {\n  background-color: #4b545c;\n}\n\n.dark-mode .jsgrid-edit-row > .jsgrid-cell,\n.dark-mode .jsgrid-filter-row > .jsgrid-cell,\n.dark-mode .jsgrid-grid-body, .dark-mode .jsgrid-grid-header,\n.dark-mode .jsgrid-header-row > .jsgrid-header-cell,\n.dark-mode .jsgrid-insert-row > .jsgrid-cell,\n.dark-mode .jsgrid-row > .jsgrid-cell,\n.dark-mode .jsgrid-alt-row > .jsgrid-cell {\n  border-color: #6c757d;\n}\n\n.dark-mode .jsgrid-header-row > .jsgrid-header-cell,\n.dark-mode .jsgrid-row > .jsgrid-cell {\n  background-color: #343a40;\n}\n\n.dark-mode .jsgrid-alt-row > .jsgrid-cell {\n  background-color: #3a4047;\n}\n\n.dark-mode .jsgrid-selected-row > .jsgrid-cell {\n  background-color: #3f474e;\n}\n/*# sourceMappingURL=adminlte.plugins.css.map */", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\n// Mixins: Miscellaneous\n//\n\n// ETC\n@mixin translate($x, $y) {\n  transform: translate($x, $y);\n}\n\n// Different radius each side\n@mixin border-radius-sides($top-left, $top-right, $bottom-left, $bottom-right) {\n  border-radius: $top-left $top-right $bottom-left $bottom-right;\n}\n\n@mixin calc($property, $expression) {\n  #{$property}: calc(#{$expression});\n}\n\n@mixin rotate($value) {\n  transform: rotate($value);\n}\n\n@mixin animation($animation) {\n  animation: $animation;\n}\n\n// Gradient background\n@mixin gradient($color: #f5f5f5, $start: #eee, $stop: $white) {\n  background-color: $color;\n  background-image: gradient(linear, left bottom, left top, color-stop(0, $start), color-stop(1, $stop));\n}\n\n//\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "//\n// Plugin: Select2\n//\n\n//Signle select\n// .select2-container--default,\n// .select2-selection {\n//   &.select2-container--focus,\n//   &:focus,\n//   &:active {\n//     outline: none;\n//   }\n// }\n\n.select2-container--default {\n  .select2-selection--single {\n    border: $input-border-width solid $input-border-color;\n    //border-radius: $input-radius;\n    padding: ($input-padding-y * 1.25) $input-padding-x;\n    height: $input-height;\n  }\n\n  &.select2-container--open {\n    .select2-selection--single {\n      border-color: lighten($primary, 25%);\n    }\n  }\n\n  & .select2-dropdown {\n    border: $input-border-width solid $input-border-color;\n    //border-radius: $input-radius;\n  }\n\n  & .select2-results__option {\n    padding: 6px 12px;\n    user-select: none;\n  }\n\n  & .select2-selection--single .select2-selection__rendered {\n    padding-left: 0;\n    //padding-right: 0;\n    height: auto;\n    margin-top: -3px;\n  }\n\n  &[dir=\"rtl\"] .select2-selection--single .select2-selection__rendered {\n    padding-right: 6px;\n    padding-left: 20px;\n  }\n\n  & .select2-selection--single .select2-selection__arrow {\n    height: 31px;\n    right: 6px;\n  }\n\n  & .select2-selection--single .select2-selection__arrow b {\n    margin-top: 0;\n  }\n\n  .select2-dropdown,\n  .select2-search--inline {\n    .select2-search__field {\n      border: $input-border-width solid $input-border-color;\n\n      &:focus {\n        outline: none;\n        border: $input-border-width solid $input-focus-border-color;\n      }\n    }\n  }\n\n  .select2-dropdown {\n    &.select2-dropdown--below {\n      border-top: 0;\n    }\n\n    &.select2-dropdown--above {\n      border-bottom: 0;\n    }\n  }\n\n  .select2-results__option {\n    &[aria-disabled='true'] {\n      color: $gray-600;\n    }\n\n    &[aria-selected='true'] {\n      $color: $gray-300;\n\n      background-color: $color;\n\n      &,\n      &:hover {\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .select2-results__option--highlighted {\n    $color: $primary;\n    background-color: $color;\n    color: color-yiq($color);\n\n    &[aria-selected] {\n      $color: darken($color, 3%);\n\n      &,\n      &:hover {\n        background-color: $color;\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  //Multiple select\n  & {\n    .select2-selection--multiple {\n      border: $input-border-width solid $input-border-color;\n      min-height: $input-height;\n\n      &:focus {\n        border-color: $input-focus-border-color;\n      }\n\n      .select2-selection__rendered {\n        padding: 0 $input-padding-x * 0.5 $input-padding-y;\n        margin-bottom: -$input-padding-x * 0.5;\n\n        li:first-child.select2-search.select2-search--inline {\n          width: 100%;\n          margin-left: $input-padding-x * 0.5;\n\n          .select2-search__field {\n            width: 100% !important;\n          }\n        }\n\n\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            border: 0;\n            margin-top: 6px;\n          }\n        }\n      }\n\n      .select2-selection__choice {\n        background-color: $primary;\n        border-color: darken($primary, 5%);\n        color: color-yiq($primary);\n        padding: 0 10px;\n        margin-top: .31rem;\n      }\n\n      .select2-selection__choice__remove {\n        color: rgba(255, 255, 255, 0.7);\n        float: right;\n        margin-left: 5px;\n        margin-right: -2px;\n\n        &:hover {\n          color: $white;\n        }\n      }\n\n      .text-sm &,\n      &.text-sm {\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            margin-top: 8px;\n          }\n        }\n\n        .select2-selection__choice {\n          margin-top: .4rem;\n        }\n      }\n    }\n\n    &.select2-container--focus {\n      .select2-selection--single,\n      .select2-selection--multiple {\n        border-color: $input-focus-border-color;\n      }\n\n      .select2-search__field {\n        border: 0;\n      }\n    }\n  }\n\n  & .select2-selection--single .select2-selection__rendered li {\n    padding-right: 10px;\n  }\n\n  .input-group-prepend ~ & {\n    .select2-selection {\n      border-bottom-left-radius: 0;\n      border-top-left-radius: 0;\n    }\n  }\n\n  .input-group > &:not(:last-child) {\n    .select2-selection {\n      border-bottom-right-radius: 0;\n      border-top-right-radius: 0;\n    }\n  }\n}\n\n// Select2 Bootstrap4 Theme overrides\n.select2-container--bootstrap4 {\n  &.select2-container--focus .select2-selection {\n    box-shadow: none;\n  }\n}\n\n// text-sm / form-control-sm override\nselect.form-control-sm ~ {\n  .select2-container--default {\n    font-size: 75%;\n  }\n}\n\n.text-sm,\nselect.form-control-sm ~ {\n  .select2-container--default {\n    .select2-selection--single {\n      height: $input-height-sm;\n\n      .select2-selection__rendered {\n        margin-top: -.4rem;\n      }\n\n      .select2-selection__arrow {\n        top: -.12rem;\n      }\n    }\n\n    .select2-selection--multiple {\n      min-height: $input-height-sm;\n\n      .select2-selection__rendered {\n        padding: 0 $input-padding-x-sm * 0.5 $input-padding-y-sm;\n        margin-top: -($input-padding-x-sm * .2);\n\n        li:first-child.select2-search.select2-search--inline {\n          margin-left: $input-padding-x-sm * 0.5;\n        }\n\n        .select2-search.select2-search--inline {\n          .select2-search__field {\n            margin-top: 6px;\n          }\n        }\n      }\n    }\n  }\n}\n\n// Dropdown Fix inside maximized card\n.maximized-card .select2-dropdown {\n  z-index: 9999;\n}\n\n// Background colors (theme colors)\n@each $name, $color in $theme-colors {\n  @include select2-variant($name, $color);\n}\n\n// Background colors (colors)\n@each $name, $color in $colors {\n  @include select2-variant($name, $color);\n}\n\n@include dark-mode () {\n  .select2-selection {\n    background-color: $dark;\n    border-color: $gray-600;\n  }\n\n  .select2-container--disabled .select2-selection--single {\n    background-color: lighten($dark, 7.5%);\n  }\n\n  .select2-selection--single {\n    background-color: $dark;\n    border-color: $gray-600;\n\n    .select2-selection__rendered {\n      color: $white;\n    }\n  }\n  .select2-dropdown .select2-search__field,\n  .select2-search--inline .select2-search__field {\n    background-color: $dark;\n    border-color: $gray-600;\n    color: white;\n  }\n  .select2-dropdown {\n    background-color: $dark;\n    border-color: $gray-600;\n    color: white;\n  }\n  .select2-results__option[aria-selected=\"true\"] {\n    background-color: lighten($dark, 5%) !important;\n    color: $gray-300;\n  }\n  .select2-container .select2-search--inline .select2-search__field {\n    background-color: transparent;\n    color: $white;\n  }\n\n  .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {\n    color: $white;\n  }\n\n  // Background colors (theme colors)\n  @each $name, $color in $theme-colors-alt {\n    @include select2-variant($name, $color);\n  }\n\n  // Background colors (colors)\n  @each $name, $color in $colors-alt {\n    @include select2-variant($name, $color);\n  }\n}\n", "//\n// General: Mixins\n//\n\n// Select2 Variant\n@mixin select2-variant($name, $color) {\n  .select2-#{$name} {\n\n    + .select2-container--default {\n      &.select2-container--open {\n        .select2-selection--single {\n          border-color: lighten($color, 25%);\n        }\n      }\n\n      &.select2-container--focus .select2-selection--single {\n        border-color: lighten($color, 25%);\n      }\n    }\n\n    .select2-container--default &,\n    .select2-container--default {\n      &.select2-dropdown,\n      .select2-dropdown,\n      .select2-search--inline {\n        .select2-search__field {\n          &:focus {\n            border: $input-border-width solid lighten($color, 25%);\n          }\n        }\n      }\n\n      .select2-results__option--highlighted {\n        background-color: $color;\n        color: color-yiq($color);\n\n        &[aria-selected] {\n          &,\n          &:hover {\n            background-color: darken($color, 3%);\n            color: color-yiq(darken($color, 3%));\n          }\n        }\n      }\n\n      //Multiple select\n      & {\n        .select2-selection--multiple {\n          &:focus {\n            border-color: lighten($color, 25%);\n          }\n\n          .select2-selection__choice {\n            background-color: $color;\n            border-color: darken($color, 5%);\n            color: color-yiq($color);\n          }\n\n          .select2-selection__choice__remove {\n            color: rgba(color-yiq($color), 0.7);\n\n            &:hover {\n              color: color-yiq($color);\n            }\n          }\n        }\n\n        &.select2-container--focus .select2-selection--multiple {\n          border-color: lighten($color, 25%);\n        }\n      }\n    }\n  }\n}\n", "//\n// Mixins: Dark Mode Controll\n//\n\n@mixin dark-mode {\n  @if $enable-dark-mode {\n    .dark-mode {\n      @content;\n    }\n  }\n}\n", "//\n// Plugin: Bootstrap Slider\n//\n\n// Tooltip fix\n.slider .tooltip.in {\n  opacity: $tooltip-opacity;\n}\n\n// Style override\n.slider {\n  &.slider-vertical {\n    height: 100%;\n  }\n  &.slider-horizontal {\n    width: 100%;\n  }\n}\n\n// Colors\n@each $name, $color in $theme-colors {\n  .slider-#{$name} .slider {\n    .slider-selection {\n      background: $color;\n    }\n  }\n}\n\n@each $name, $color in $colors {\n  .slider-#{$name} .slider {\n    .slider-selection {\n      background: $color;\n    }\n  }\n}\n\n@include dark-mode () {\n  .slider-track {\n    background-color: lighten($dark, 10%);\n    background-image: none;\n  }\n\n  @each $name, $color in $theme-colors-alt {\n    .slider-#{$name} .slider {\n      .slider-selection {\n        background: $color;\n      }\n    }\n  }\n\n  @each $name, $color in $colors-alt {\n    .slider-#{$name} .slider {\n      .slider-selection {\n        background: $color;\n      }\n    }\n  }\n}\n", "//\n// Plugin: iCheck Bootstrap\n//\n\n// iCheck colors (theme colors)\n@each $name, $color in $theme-colors {\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:checked + label::before,\n  .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n    background-color: #{$color};\n    border-color: #{$color};\n  }\n}\n\n// iCheck colors (colors)\n@each $name, $color in $colors {\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n  .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n    border-color: #{$color};\n  }\n\n  .icheck-#{$name} > input:first-child:checked + label::before,\n  .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n    background-color: #{$color};\n    border-color: #{$color};\n  }\n}\n\n@include dark-mode () {\n  [class*=\"icheck-\"] > input:first-child:not(:checked) {\n    + input[type=\"hidden\"] + label::before,\n    + label::before {\n      border-color: $gray-600;\n    }\n  }\n  // iCheck colors (theme colors)\n  @each $name, $color in $theme-colors-alt {\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n      border-color: #{$color};\n    }\n\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n      border-color: #{$color};\n    }\n\n    .icheck-#{$name} > input:first-child:checked + label::before,\n    .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n      background-color: #{$color};\n      border-color: #{$color};\n    }\n  }\n\n  // iCheck colors (colors)\n  @each $name, $color in $colors-alt {\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + label::before,\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):hover + input[type=\"hidden\"] + label::before {\n      border-color: #{$color};\n    }\n\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + label::before,\n    .icheck-#{$name} > input:first-child:not(:checked):not(:disabled):focus + input[type=\"hidden\"] + label::before {\n      border-color: #{$color};\n    }\n\n    .icheck-#{$name} > input:first-child:checked + label::before,\n    .icheck-#{$name} > input:first-child:checked + input[type=\"hidden\"] + label::before {\n      background-color: #{$color};\n      border-color: #{$color};\n    }\n  }\n}\n", "//\n// Plugins: jQ<PERSON>y <PERSON>\n//\n\n.mapael {\n  .map {\n    position: relative;\n  }\n\n  .mapTooltip {\n    @include reset-text();\n    @include border-radius($tooltip-border-radius);\n    @include font-size($tooltip-font-size);\n    background-color: $tooltip-bg;\n    color: $tooltip-color;\n    display: block;\n    max-width: $tooltip-max-width;\n    padding: $tooltip-padding-y $tooltip-padding-x;\n    position: absolute;\n    text-align: center;\n    word-wrap: break-word;\n    z-index: $zindex-tooltip;\n  }\n\n  .myLegend {\n    background-color: $gray-100;\n    border: 1px solid $gray-500;\n    padding: 10px;\n    width: 600px;\n  }\n\n  .zoomButton {\n    background-color: $button-default-background-color;\n    border: 1px solid $button-default-border-color;\n    border-radius: $btn-border-radius;\n    color: $button-default-color;\n    cursor: pointer;\n    font-weight: 700;\n    height: 16px;\n    left: 10px;\n    line-height: 14px;\n    padding-left: 1px;\n    position: absolute;\n    text-align: center;\n    top: 0;\n\n    user-select: none;\n    width: 16px;\n\n    &:hover,\n    &:active,\n    &.hover {\n      background-color: darken($button-default-background-color, 5%);\n      color: darken($button-default-color, 10%);\n    }\n  }\n\n  .zoomReset {\n    line-height: 12px;\n    top: 10px;\n  }\n\n  .zoomIn {\n    top: 30px;\n  }\n\n  .zoomOut {\n    top: 50px;\n  }\n}\n", "@mixin reset-text() {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n}\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated responsive font sizes\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/v8.x/LICENSE)\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n@if $rfs-font-size-unit != rem and $rfs-font-size-unit != px {\n  @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize font size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: divide($rfs-base-font-size, $rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: divide($rfs-base-font-size, divide($rfs-base-font-size * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-disable-class {\n  @if $rfs-class == \"disable\" {\n    // Adding an extra class increases specificity, which prevents the media query to override the font size\n    &,\n    .disable-responsive-font-size &,\n    &.disable-responsive-font-size {\n      @content;\n    }\n  }\n  @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-enable-class {\n  @if $rfs-class == \"enable\" {\n    .enable-responsive-font-size &,\n    &.enable-responsive-font-size {\n      @content;\n    }\n  }\n  @else {\n    @content;\n  }\n}\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query($mq-value) {\n  @if $rfs-two-dimensional {\n    @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n      @content;\n    }\n  }\n  @else {\n    @media (max-width: #{$mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Responsive font size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Remove unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: divide($fs, $fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: divide($fs, divide($fs * 0 + 1, $rfs-rem-value));\n    }\n\n    // Set default font size\n    $rfs-static: if($rfs-font-size-unit == rem, #{divide($fs, $rfs-rem-value)}rem, #{$fs}px);\n\n    // Only add the media query if the font size is bigger than the minimum font size\n    @if $fs <= $rfs-base-font-size or not $enable-responsive-font-sizes {\n      font-size: #{$rfs-static}#{$rfs-suffix};\n    }\n    @else {\n      // Calculate the minimum font size for $fs\n      $fs-min: $rfs-base-font-size + divide($fs - $rfs-base-font-size, $rfs-factor);\n\n      // Calculate difference between $fs and the minimum font size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      $min-width: if($rfs-font-size-unit == rem, #{divide($fs-min, $rfs-rem-value)}rem, #{$fs-min}px);\n\n      // Use `vmin` if two-dimensional is enabled\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{divide($fs-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n      // Set the calculated font-size\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n\n      // Breakpoint formatting\n      $mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n\n      @include _rfs-disable-class {\n        font-size: #{$rfs-static}#{$rfs-suffix};\n      }\n\n      @include _rfs-media-query($mq-value) {\n        @include _rfs-enable-class {\n          font-size: $rfs-fluid;\n        }\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixins use RFS to rescale the font size\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "//\n// Plugins: JQVMap\n//\n\n// Zoom Button size fixes\n.jqvmap-zoomin,\n.jqvmap-zoomout {\n  background-color: $button-default-background-color;\n  border: 1px solid $button-default-border-color;\n  border-radius: $btn-border-radius;\n  color: $button-default-color;\n  height: 15px;\n  width: 15px;\n  padding: 1px 2px;\n\n  &:hover,\n  &:active,\n  &.hover {\n    background-color: darken($button-default-background-color, 5%);\n    color: darken($button-default-color, 10%);\n  }\n}\n", "//\n// Plugin: SweetAlert2\n//\n\n// Icon Colors\n.swal2-icon {\n  &.swal2-info {\n    border-color: ligthen($info, 20%);\n    color: $info;\n  }\n\n  &.swal2-warning {\n    border-color: ligthen($warning, 20%);\n    color: $warning;\n  }\n\n  &.swal2-error {\n    border-color: ligthen($danger, 20%);\n    color: $danger;\n  }\n\n  &.swal2-question {\n    border-color: ligthen($secondary, 20%);\n    color: $secondary;\n  }\n\n  &.swal2-success {\n    border-color: ligthen($success, 20%);\n    color: $success;\n\n    .swal2-success-ring {\n      border-color: ligthen($success, 20%);\n    }\n\n    [class^='swal2-success-line'] {\n      background-color: $success;\n    }\n  }\n}\n\n@include dark-mode () {\n  .swal2-popup {\n    background-color: $dark;\n    color: $gray-200;\n\n    .swal2-content,\n    .swal2-title {\n      color: $gray-200;\n    }\n  }\n}\n", "//\n// Plugin: Toastr\n//\n\n// Background to FontAwesome Icons\n// #toast-container > .toast {\n//     background-image: none !important;\n// }\n// #toast-container > .toast .toast-message:before {\n//     font-family: 'Font Awesome 5 Free';\n//     font-size: 24px;\n//     font-weight: 900;\n//     line-height: 18px;\n//     float: left;\n//     color: $white;\n//     padding-right: 0.5em;\n//     margin: auto 0.5em auto -1.5em;\n// }\n// #toast-container > .toast-warning .toast-message:before {\n//     content: \"\\f06a\";\n// }\n// #toast-container > .toast-error .toast-message:before {\n//     content: \"\\f071\";\n// }\n// #toast-container > .toast-info .toast-message:before {\n//     content: \"\\f05a\";\n// }\n// #toast-container > .toast-success .toast-message:before {\n//     content: \"\\f058\";\n// }\n\n\n#toast-container {\n  // Background color\n  .toast {\n    background-color: $primary;\n  }\n\n  .toast-success {\n    background-color: $success;\n  }\n\n  .toast-error {\n    background-color: $danger;\n  }\n\n  .toast-info {\n    background-color: $info;\n  }\n\n  .toast-warning {\n    background-color: $warning;\n  }\n}\n\n// full width fix\n.toast-bottom-full-width .toast,\n.toast-top-full-width .toast {\n  max-width: inherit;\n}\n", "//\n// Plugin: Pace\n//\n\n.pace {\n  z-index: $zindex-main-sidebar + 10;\n\n  .pace-progress {\n    z-index: $zindex-main-sidebar + 11;\n  }\n\n  .pace-activity {\n    z-index: $zindex-main-sidebar + 12;\n  }\n}\n\n// Mixin\n@mixin pace-variant($name, $color) {\n  .pace-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-barber-shop-#{$name} {\n    .pace {\n      background: color-yiq($color);\n\n      .pace-progress {\n        background: $color;\n      }\n\n      .pace-activity {\n        background-image: linear-gradient(45deg, rgba(color-yiq($color), 0.2) 25%, transparent 25%, transparent 50%, rgba(color-yiq($color), 0.2) 50%, rgba(color-yiq($color), 0.2) 75%, transparent 75%, transparent);\n      }\n    }\n  }\n\n  .pace-big-counter-#{$name} {\n    .pace {\n      .pace-progress::after {\n        color: rgba($color, .19999999999999996);\n      }\n    }\n  }\n\n  .pace-bounce-#{$name} {\n    .pace {\n      .pace-activity {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-center-atom-#{$name} {\n    .pace-progress {\n      height: 100px;\n      width: 80px;\n\n      &::before {\n        background: $color;\n        color: color-yiq($color);\n        font-size: .8rem;\n        line-height: .7rem;\n        padding-top: 17%;\n      }\n    }\n\n    .pace-activity {\n      border-color: $color;\n\n      &::after,\n      &::before {\n        border-color: $color;\n      }\n    }\n  }\n\n  .pace-center-circle-#{$name} {\n    .pace {\n      .pace-progress {\n        background: rgba($color, .8);\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .pace-center-radar-#{$name} {\n    .pace {\n      .pace-activity {\n        border-color: $color transparent transparent;\n      }\n\n      .pace-activity::before {\n        border-color: $color transparent transparent;\n      }\n    }\n  }\n\n  .pace-center-simple-#{$name} {\n    .pace {\n      background: color-yiq($color);\n      border-color: $color;\n\n      .pace-progress {\n        background: $color;\n      }\n    }\n  }\n\n  .pace-material-#{$name} {\n    .pace {\n      color: $color;\n    }\n  }\n\n  .pace-corner-indicator-#{$name} {\n    .pace {\n      .pace-activity {\n        background: $color;\n      }\n\n      .pace-activity::after,\n      .pace-activity::before {\n        border: 5px solid color-yiq($color);\n      }\n\n\n      .pace-activity::before {\n          border-right-color: rgba($color, .2);\n          border-left-color: rgba($color, .2);\n      }\n\n      .pace-activity::after {\n          border-top-color: rgba($color, .2);\n          border-bottom-color: rgba($color, .2);\n      }\n    }\n  }\n\n  .pace-fill-left-#{$name} {\n    .pace {\n      .pace-progress {\n        background-color: rgba($color, 0.19999999999999996);\n      }\n    }\n  }\n\n  .pace-flash-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n      }\n\n      .pace-progress-inner {\n        box-shadow: 0 0 10px $color, 0 0 5px $color;\n      }\n\n      .pace-activity {\n        border-top-color: $color;\n        border-left-color: $color;\n      }\n    }\n  }\n\n  .pace-loading-bar-#{$name} {\n    .pace {\n      .pace-progress {\n        background: $color;\n        color: $color;\n        box-shadow: 120px 0 color-yiq($color), 240px 0 color-yiq($color);\n      }\n\n      .pace-activity {\n        box-shadow: inset 0 0 0 2px $color, inset 0 0 0 7px color-yiq($color);\n      }\n    }\n  }\n\n  .pace-mac-osx-#{$name} {\n    .pace {\n      .pace-progress {\n        background-color: $color;\n        box-shadow: inset -1px 0 $color, inset 0 -1px $color, inset 0 2px rgba(color-yiq($color), 0.5), inset 0 6px rgba(color-yiq($color), .3);\n      }\n\n      .pace-activity {\n        background-image: radial-gradient(rgba(color-yiq($color), .65) 0%, rgba(color-yiq($color), .15) 100%);\n        height: 12px;\n      }\n    }\n  }\n\n  .pace-progress-color-#{$name} {\n    .pace-progress {\n      color: $color;\n    }\n  }\n}\n\n\n@each $name, $color in $theme-colors {\n  @include pace-variant($name, $color);\n}\n\n@each $name, $color in $colors {\n  @include pace-variant($name, $color);\n}\n\n", "/**\n  * bootstrap-switch - Turn checkboxes and radio buttons into toggle switches.\n  *\n  * @version v3.4 (MODDED)\n  * @homepage https://bttstrp.github.io/bootstrap-switch\n  * <AUTHOR> <<EMAIL>> (http://larentis.eu)\n  * @license MIT\n  */\n\n$bootstrap-switch-border-radius: $btn-border-radius;\n$bootstrap-switch-handle-border-radius: .1rem;\n\n.bootstrap-switch {\n  border: $input-border-width solid $input-border-color;\n  border-radius: $bootstrap-switch-border-radius;\n  cursor: pointer;\n  direction: ltr;\n  display: inline-block;\n  line-height: .5rem;\n  overflow: hidden;\n  position: relative;\n  text-align: left;\n  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;\n  user-select: none;\n  vertical-align: middle;\n  z-index: 0;\n\n  .bootstrap-switch-container {\n    border-radius: $bootstrap-switch-border-radius;\n    display: inline-block;\n    top: 0;\n    transform: translate3d(0, 0, 0);\n\n  }\n\n  &:focus-within {\n    box-shadow: $input-btn-focus-box-shadow;\n  }\n\n  .bootstrap-switch-handle-on,\n  .bootstrap-switch-handle-off,\n  .bootstrap-switch-label {\n    box-sizing: border-box;\n    cursor: pointer;\n    display: table-cell;\n    font-size: 1rem;\n    font-weight: 500;\n    line-height: 1.2rem;\n    padding: .25rem .5rem;\n    vertical-align: middle;\n  }\n\n  .bootstrap-switch-handle-on,\n  .bootstrap-switch-handle-off {\n    text-align: center;\n    z-index: 1;\n\n    &.bootstrap-switch-default {\n      background: $gray-200;\n      color: color-yiq($gray-200);\n    }\n\n    @each $name, $color in $theme-colors {\n      &.bootstrap-switch-#{$name} {\n        background: $color;\n        color: color-yiq($color);\n      }\n    }\n\n    @each $name, $color in $colors {\n      &.bootstrap-switch-#{$name} {\n        background: $color;\n        color: color-yiq($color);\n      }\n    }\n  }\n\n  .bootstrap-switch-handle-on {\n    border-bottom-left-radius: $bootstrap-switch-handle-border-radius;\n    border-top-left-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  .bootstrap-switch-handle-off {\n    border-bottom-right-radius: $bootstrap-switch-handle-border-radius;\n    border-top-right-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  input[type='radio'],\n  input[type='checkbox'] {\n    filter: alpha(opacity=0);\n    left: 0;\n    margin: 0;\n    opacity: 0;\n    position: absolute;\n    top: 0;\n    visibility: hidden;\n    z-index: -1;\n  }\n\n  &.bootstrap-switch-mini {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: .875rem;\n      line-height: 1.5;\n      padding: .1rem .3rem;\n    }\n  }\n\n  &.bootstrap-switch-small {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: .875rem;\n      line-height: 1.5;\n      padding: .2rem .4rem;\n    }\n  }\n\n  &.bootstrap-switch-large {\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      font-size: 1.25rem;\n      line-height: 1.3333333rem;\n      padding: .3rem .5rem;\n    }\n  }\n\n  &.bootstrap-switch-disabled,\n  &.bootstrap-switch-readonly,\n  &.bootstrap-switch-indeterminate {\n    cursor: default;\n\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off,\n    .bootstrap-switch-label {\n      cursor: default;\n      filter: alpha(opacity=50);\n      opacity: .5;\n    }\n  }\n\n  &.bootstrap-switch-animate .bootstrap-switch-container {\n    transition: margin-left .5s;\n  }\n\n  &.bootstrap-switch-inverse {\n    .bootstrap-switch-handle-on {\n      border-radius: 0 $bootstrap-switch-handle-border-radius $bootstrap-switch-handle-border-radius 0;\n    }\n\n    .bootstrap-switch-handle-off {\n      border-radius: $bootstrap-switch-handle-border-radius 0 0 $bootstrap-switch-handle-border-radius;\n    }\n  }\n\n  // &.bootstrap-switch-focused {\n  //   border-color: $input-btn-focus-color;\n  //   box-shadow: $input-btn-focus-box-shadow;\n  //   outline: 0;\n  // }\n\n  &.bootstrap-switch-on .bootstrap-switch-label,\n  &.bootstrap-switch-inverse.bootstrap-switch-off .bootstrap-switch-label {\n    border-bottom-right-radius: $bootstrap-switch-handle-border-radius;\n    border-top-right-radius: $bootstrap-switch-handle-border-radius;\n  }\n\n  &.bootstrap-switch-off .bootstrap-switch-label,\n  &.bootstrap-switch-inverse.bootstrap-switch-on .bootstrap-switch-label {\n    border-bottom-left-radius: $bootstrap-switch-handle-border-radius;\n    border-top-left-radius: $bootstrap-switch-handle-border-radius;\n  }\n}\n\n@include dark-mode () {\n  .bootstrap-switch {\n    border-color: $gray-600;\n\n    .bootstrap-switch-handle-off.bootstrap-switch-default,\n    .bootstrap-switch-handle-on.bootstrap-switch-default {\n      background-color: lighten($dark, 2.5%);\n      color: $white;\n      border-color: lighten($dark, 7.5%);\n    }\n    .bootstrap-switch-handle-on,\n    .bootstrap-switch-handle-off {\n      @each $name, $color in $theme-colors-alt {\n        &.bootstrap-switch-#{$name} {\n          background: $color;\n          color: color-yiq($color);\n        }\n      }\n\n      @each $name, $color in $colors-alt {\n        &.bootstrap-switch-#{$name} {\n          background: $color;\n          color: color-yiq($color);\n        }\n      }\n    }\n  }\n}\n", "@include dark-mode() {\n  .daterangepicker {\n    background-color: lighten($dark, 5%);\n    border: inherit;\n\n    &::before,\n    &::after {\n      border-bottom-color: lighten($dark, 5%);\n    }\n\n    td.available:hover,\n    th.available:hover {\n      background-color: lighten($dark, 5%);\n    }\n    td.in-range {\n      background-color: lighten($dark, 10%);\n      color: $white;\n    }\n\n    td.off,\n    td.off.in-range,\n    td.off.start-date,\n    td.off.end-date {\n      background-color: darken($dark, 5%);\n      color: $white;\n    }\n\n    .ranges li:hover {\n      background-color: $dark;\n    }\n\n    &.show-ranges.ltr .drp-calendar {\n      border-color: lighten($dark, 10%);\n\n      &.left,\n      &.right {\n        border-color: lighten($dark, 10%);\n        padding-top: 0;\n      }\n    }\n\n    .drp-buttons {\n      border-color: lighten($dark, 10%);\n    }\n\n    .calendar-table {\n      background-color: $dark;\n      border-color: lighten($dark, 10%);\n\n      th,\n      td {\n        color: $white;\n      }\n\n      .next span,\n      .prev span {\n        border-color: $white;\n      }\n    }\n\n    select.hourselect,\n    select.minuteselect,\n    select.secondselect,\n    select.ampmselect {\n      background-color: $dark;\n      border-color: lighten($dark, 10%);\n    }\n  }\n}\n", "//\n// Plugins: Miscellaneous\n// Old plugin codes\n//\n\n// _fix for sparkline tooltip\n.jqstooltip {\n  height: auto !important;\n  padding: 5px !important;\n  width: auto !important;\n}\n\n// jQueryUI\n.connectedSortable {\n  min-height: 100px;\n}\n\n.ui-helper-hidden-accessible {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sort-highlight {\n  background: $gray-100;\n  border: 1px dashed $gray-300;\n  margin-bottom: 10px;\n}\n\n// Charts\n.chart {\n  overflow: hidden;\n  position: relative;\n}\n\n\n@include dark-mode () {\n  .irs--flat .irs-line {\n    background-color: lighten($dark, 10%);\n  }\n  .jsgrid-edit-row > .jsgrid-cell,\n  .jsgrid-filter-row > .jsgrid-cell,\n  .jsgrid-grid-body, .jsgrid-grid-header,\n  .jsgrid-header-row > .jsgrid-header-cell,\n  .jsgrid-insert-row > .jsgrid-cell,\n  .jsgrid-row > .jsgrid-cell,\n  .jsgrid-alt-row > .jsgrid-cell {\n    border-color: $gray-600;\n  }\n  .jsgrid-header-row > .jsgrid-header-cell,\n  .jsgrid-row > .jsgrid-cell {\n    background-color: $dark;\n  }\n  .jsgrid-alt-row > .jsgrid-cell {\n    background-color: lighten($dark, 2.5%);\n  }\n  .jsgrid-selected-row > .jsgrid-cell {\n    background-color: lighten($dark, 5%);\n  }\n}\n"]}